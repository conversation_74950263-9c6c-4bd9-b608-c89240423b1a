/**
 * Email View Component
 *
 * Provides functionality for displaying emails in a card-based layout
 * with AI-powered features for smart inbox management
 */

class EmailView {
  constructor() {
    this.expanded = new Set();
    this.viewMode = 'priority'; // 'priority', 'cluster', 'chronological'
    this.filterMode = 'all'; // 'all', 'high', 'medium', 'low'
    console.log('EmailView initialized');
  }

  /**
   * Render emails in a card-based layout
   * @param {Array} emails - Array of email objects
   * @param {HTMLElement} container - Container element to render emails in
   */
  renderEmails(emails, container) {
    try {
      console.log('Rendering emails:', emails);
      console.log('Container:', container);

      if (!container) {
        console.error('Email container is null or undefined');
        return;
      }

      // Process emails with AI
      let processedData;
      try {
        processedData = window.emailAI ? window.emailAI.processEmails(emails) : { emails };
        console.log('AI processed data:', processedData);
      } catch (error) {
        console.error('Error processing emails with AI:', error);
        processedData = { emails: emails || [] };
      }

      if (!emails || !emails.length) {
        container.innerHTML = '<div class="text-gray-400">No emails found</div>';
        return;
      }

    // Get emails to display based on current filter
    const filteredEmails = this.filterMode === 'all'
      ? processedData.emails
      : processedData.emails.filter(email => email.priority === this.filterMode);

    // Create email container
    const emailContainer = document.createElement('div');
    emailContainer.className = 'email-container';

    // Add controls and stats
    const controlsDiv = document.createElement('div');
    controlsDiv.className = 'email-controls mb-4';

    // Add view mode selector
    const viewModeSelector = document.createElement('div');
    viewModeSelector.className = 'view-mode-selector mb-2';
    viewModeSelector.innerHTML = `
      <div class="flex items-center justify-between">
        <div>
          <span class="text-gray-300 mr-2">View:</span>
          <select class="view-mode-select bg-gray-800 text-white border border-gray-700 rounded px-2 py-1">
            <option value="priority" ${this.viewMode === 'priority' ? 'selected' : ''}>Priority</option>
            <option value="cluster" ${this.viewMode === 'cluster' ? 'selected' : ''}>Clustered</option>
            <option value="chronological" ${this.viewMode === 'chronological' ? 'selected' : ''}>Chronological</option>
          </select>
        </div>
        <div>
          <span class="text-gray-300 mr-2">Filter:</span>
          <select class="filter-mode-select bg-gray-800 text-white border border-gray-700 rounded px-2 py-1">
            <option value="all" ${this.filterMode === 'all' ? 'selected' : ''}>All</option>
            <option value="high" ${this.filterMode === 'high' ? 'selected' : ''}>High Priority</option>
            <option value="medium" ${this.filterMode === 'medium' ? 'selected' : ''}>Medium Priority</option>
            <option value="low" ${this.filterMode === 'low' ? 'selected' : ''}>Low Priority</option>
          </select>
        </div>
      </div>
    `;
    controlsDiv.appendChild(viewModeSelector);

    // Add stats
    if (processedData.stats) {
      const statsDiv = document.createElement('div');
      statsDiv.className = 'email-stats mb-3 p-3 bg-gray-800 rounded';

      const needsAttention = processedData.stats.needsAttention;
      const highCount = processedData.stats.byPriority.high;
      const mediumCount = processedData.stats.byPriority.medium;
      const lowCount = processedData.stats.byPriority.low;

      statsDiv.innerHTML = `
        <div class="text-sm">
          <div class="flex justify-between items-center mb-2">
            <span class="text-gray-300">Smart Inbox Summary</span>
            <span class="email-badge">${emails.length} total</span>
          </div>
          <div class="flex justify-between">
            <div>
              <span class="inline-block w-3 h-3 rounded-full bg-red-500 mr-1"></span>
              <span class="text-gray-300">${highCount} high priority</span>
            </div>
            <div>
              <span class="inline-block w-3 h-3 rounded-full bg-yellow-500 mr-1"></span>
              <span class="text-gray-300">${mediumCount} medium</span>
            </div>
            <div>
              <span class="inline-block w-3 h-3 rounded-full bg-blue-500 mr-1"></span>
              <span class="text-gray-300">${lowCount} low</span>
            </div>
          </div>
          ${needsAttention > 0 ? `<div class="mt-2 text-red-400"><i class="fas fa-exclamation-circle mr-1"></i>${needsAttention} emails need your attention</div>` : ''}
        </div>
      `;

      controlsDiv.appendChild(statsDiv);
    }

    emailContainer.appendChild(controlsDiv);

    // Show clusters if in cluster view mode
    if (this.viewMode === 'cluster' && processedData.clusters) {
      const clusterContainer = document.createElement('div');
      clusterContainer.className = 'cluster-container';

      // Get clusters with multiple emails
      const significantClusters = Object.values(processedData.clusters)
        .filter(cluster => cluster.count > 1)
        .sort((a, b) => b.count - a.count);

      if (significantClusters.length > 0) {
        significantClusters.forEach(cluster => {
          const clusterDiv = document.createElement('div');
          clusterDiv.className = 'cluster-card mb-4 p-3 bg-gray-800 rounded';

          clusterDiv.innerHTML = `
            <div class="flex justify-between items-center mb-2">
              <div>
                <span class="text-white font-medium">${cluster.type === 'conversation' ? cluster.subject : cluster.sender}</span>
                <span class="ml-2 email-badge">${cluster.count}</span>
              </div>
              <button class="cluster-expand-btn text-gray-400 hover:text-white">
                <i class="fas fa-chevron-down"></i>
              </button>
            </div>
            <div class="cluster-summary text-gray-300 text-sm">${cluster.summary || ''}</div>
            <div class="cluster-emails hidden mt-3"></div>
          `;

          clusterContainer.appendChild(clusterDiv);

          // Add event listener to expand button
          const expandBtn = clusterDiv.querySelector('.cluster-expand-btn');
          const emailsContainer = clusterDiv.querySelector('.cluster-emails');

          expandBtn.addEventListener('click', () => {
            if (emailsContainer.classList.contains('hidden')) {
              // Expand
              emailsContainer.classList.remove('hidden');
              expandBtn.innerHTML = '<i class="fas fa-chevron-up"></i>';

              // Add emails to container
              cluster.emails.forEach((email, index) => {
                const emailCard = this.createEmailCard(email, `cluster-${index}`);
                emailCard.classList.add('mt-2');
                emailsContainer.appendChild(emailCard);
              });
            } else {
              // Collapse
              emailsContainer.classList.add('hidden');
              expandBtn.innerHTML = '<i class="fas fa-chevron-down"></i>';
              emailsContainer.innerHTML = '';
            }
          });
        });
      } else {
        clusterContainer.innerHTML = '<div class="text-gray-400 text-center">No significant clusters found</div>';
      }

      emailContainer.appendChild(clusterContainer);
    } else {
      // Create email cards
      filteredEmails.forEach((email, index) => {
        const emailCard = this.createEmailCard(email, index);
        emailContainer.appendChild(emailCard);
      });
    }

    // Clear container and add email container
    container.innerHTML = '';
    container.appendChild(emailContainer);

    // Add event listeners for controls
    const viewModeSelect = container.querySelector('.view-mode-select');
    if (viewModeSelect) {
      viewModeSelect.addEventListener('change', (e) => {
        this.viewMode = e.target.value;
        this.renderEmails(emails, container);
      });
    }

    const filterModeSelect = container.querySelector('.filter-mode-select');
    if (filterModeSelect) {
      filterModeSelect.addEventListener('change', (e) => {
        this.filterMode = e.target.value;
        this.renderEmails(emails, container);
      });
    }

    // Add event listeners
    try {
      this.addEventListeners();
    } catch (listenerError) {
      console.error('Error adding event listeners:', listenerError);
    }
    } catch (error) {
      console.error('Error rendering emails:', error);
      // Show error message in container
      if (container) {
        container.innerHTML = `
          <div class="p-4 bg-red-900 text-white rounded mb-4">
            <p><i class="fas fa-exclamation-triangle mr-2"></i> Error displaying emails</p>
            <p class="text-sm mt-2">Please try again or contact support if the problem persists.</p>
          </div>
        `;
      }
    }
  }

  /**
   * Create an email card element
   * @param {Object} email - Email object
   * @param {number|string} index - Index of the email
   * @returns {HTMLElement} - Email card element
   */
  createEmailCard(email, index) {
    const card = document.createElement('div');
    card.className = 'email-card';
    card.dataset.emailIndex = index;
    card.dataset.emailId = email.id;

    // Add classes based on email properties
    if (email.unread) {
      card.classList.add('unread');
    }

    // Add priority class if available
    if (email.priority) {
      card.classList.add(`priority-${email.priority}`);
    }

    // Format date
    const date = new Date(email.date);
    const formattedDate = this.formatDate(date);

    // Priority icon
    let priorityIcon = '';
    if (email.priority === 'high') {
      priorityIcon = '<span class="priority-indicator high"><i class="fas fa-exclamation-circle"></i></span>';
    } else if (email.priority === 'medium') {
      priorityIcon = '<span class="priority-indicator medium"><i class="fas fa-arrow-circle-up"></i></span>';
    } else if (email.priority === 'low') {
      priorityIcon = '<span class="priority-indicator low"><i class="fas fa-arrow-circle-down"></i></span>';
    }

    // Category badge
    const categoryBadge = email.category ? 
      `<span class="category-badge ${email.category.toLowerCase()}">${email.category}</span>` : '';

    // Create card content with improved layout
    card.innerHTML = `
      <div class="email-header">
        <div class="email-sender-container">
          ${priorityIcon}
          <div class="email-sender">${this.formatSender(email.from)}</div>
          ${categoryBadge}
        </div>
        <div class="email-date">${formattedDate}</div>
      </div>
      <div class="email-subject">${email.subject || 'No Subject'}</div>
      <div class="email-snippet">${this.formatSnippet(email.snippet)}</div>
      ${email.priorityReasons ? `
        <div class="email-priority-reasons">
          <div class="ai-analysis">
            <i class="fas fa-robot"></i> AI analysis: ${email.priority} priority (score: ${email.priorityScore})
          </div>
        </div>
      ` : ''}
      <div class="email-actions">
        <button class="email-action-button toggle-expand" data-index="${index}">
          <i class="fas fa-chevron-down"></i> More
        </button>
        <button class="email-action-button mark-read" data-index="${index}" data-id="${email.id}">
          <i class="fas fa-check"></i> Mark read
        </button>
        <button class="email-action-button open-gmail" data-index="${index}" data-id="${email.id}">
          <i class="fas fa-external-link-alt"></i> Open in Gmail
        </button>
        ${email.priority ? `
          <button class="email-action-button priority-action" data-index="${index}" data-id="${email.id}" data-priority="${email.priority}">
            <i class="fas ${email.priority === 'high' ? 'fa-arrow-down' : 'fa-arrow-up'}"></i>
            ${email.priority === 'high' ? 'Lower' : 'Raise'} priority
          </button>
        ` : ''}
      </div>
      <div class="email-expanded" data-index="${index}"></div>
    `;

    return card;
  }

  /**
   * Format sender name and email
   * @param {string} sender - Sender string (e.g. "Name <<EMAIL>>")
   * @returns {string} - Formatted sender
   */
  formatSender(sender) {
    if (!sender) return 'Unknown Sender';

    // Try to extract name and email
    const match = sender.match(/^"?([^"<]+)"?\s*<?([^>]*)>?$/);
    if (match) {
      const name = match[1].trim();
      const email = match[2].trim();

      if (email) {
        return `${name} <span class="sender-email">&lt;${email}&gt;</span>`;
      } else {
        return name;
      }
    }

    return sender;
  }

  /**
   * Extract and format email snippet
   * @param {string} snippet - Raw email snippet
   * @returns {string} - Formatted snippet
   */
  formatSnippet(snippet) {
    if (!snippet) return 'No preview available';
    
    // Clean up the snippet
    let formatted = snippet
      .replace(/(\r\n|\n|\r)/gm, " ")  // Replace newlines with spaces
      .replace(/\s+/g, " ")           // Replace multiple spaces with single space
      .trim();
    
    // Truncate if too long
    if (formatted.length > 200) {
      formatted = formatted.substring(0, 197) + '...';
    }
    
    return formatted;
  }

  /**
   * Add event listeners to email cards
   */
  addEventListeners() {
    // Toggle expand buttons
    document.querySelectorAll('.toggle-expand').forEach(button => {
      button.addEventListener('click', (e) => {
        const index = e.currentTarget.dataset.index;
        this.toggleExpand(index);
      });
    });

    // Mark as read buttons
    document.querySelectorAll('.mark-read').forEach(button => {
      button.addEventListener('click', (e) => {
        const id = e.currentTarget.dataset.id;
        this.markAsRead(id, e.currentTarget);
      });
    });

    // Open in Gmail buttons
    document.querySelectorAll('.open-gmail').forEach(button => {
      button.addEventListener('click', (e) => {
        const id = e.currentTarget.dataset.id;
        this.openInGmail(id);
      });
    });

    // Priority action buttons
    document.querySelectorAll('.priority-action').forEach(button => {
      button.addEventListener('click', (e) => {
        const id = e.currentTarget.dataset.id;
        const currentPriority = e.currentTarget.dataset.priority;
        const card = e.currentTarget.closest('.email-card');
        const email = this.getEmailFromCard(card);

        // Toggle priority
        const newPriority = currentPriority === 'high' ? 'medium' : 'high';
        this.changePriority(id, newPriority, e.currentTarget, email);
      });
    });
  }

  /**
   * Get email data from card element
   * @param {HTMLElement} card - Email card element
   * @returns {Object|null} - Email object or null if not found
   */
  getEmailFromCard(card) {
    if (!card) return null;

    try {
      // Extract data from card
      const sender = card.querySelector('.email-sender').textContent;
      const subject = card.querySelector('.email-subject').textContent;
      const snippet = card.querySelector('.email-snippet').textContent;
      const id = card.dataset.emailId;

      return {
        id,
        from: sender,
        subject,
        snippet
      };
    } catch (error) {
      console.error('Error extracting email data from card:', error);
      return null;
    }
  }

  /**
   * Change email priority
   * @param {string} id - Email ID
   * @param {string} newPriority - New priority ('high', 'medium', 'low')
   * @param {HTMLElement} button - Button element
   * @param {Object} email - Email object
   */
  changePriority(id, newPriority, button, email) {
    // Show loading state
    const originalHTML = button.innerHTML;
    button.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
    button.disabled = true;

    // Learn from this action
    if (window.emailAI && email) {
      const action = newPriority === 'high' ? 'mark_important' : 'mark_not_important';
      window.emailAI.learnFromAction(id, action, email);
    }

    // Update UI
    setTimeout(() => {
      const card = button.closest('.email-card');

      // Remove old priority class
      card.classList.remove('priority-high', 'priority-medium', 'priority-low');

      // Add new priority class
      card.classList.add(`priority-${newPriority}`);

      // Update priority indicator
      const priorityIndicator = card.querySelector('.priority-indicator');
      if (priorityIndicator) {
        const newIcon = newPriority === 'high'
          ? '<i class="fas fa-exclamation-circle"></i>'
          : (newPriority === 'medium' ? '<i class="fas fa-circle"></i>' : '<i class="fas fa-arrow-down"></i>');

        priorityIndicator.className = `priority-indicator ${newPriority}`;
        priorityIndicator.title = `${newPriority.charAt(0).toUpperCase() + newPriority.slice(1)} Priority`;
        priorityIndicator.innerHTML = newIcon;
      }

      // Update button
      button.dataset.priority = newPriority;
      button.innerHTML = `<i class="fas ${newPriority === 'high' ? 'fa-arrow-down' : 'fa-arrow-up'}"></i> ${newPriority === 'high' ? 'Lower' : 'Raise'} priority`;
      button.disabled = false;

      // Show confirmation
      const confirmationDiv = document.createElement('div');
      confirmationDiv.className = 'text-xs text-green-400 mt-1 mb-1 ml-2';
      confirmationDiv.innerHTML = `<i class="fas fa-check-circle mr-1"></i> Priority updated to ${newPriority}`;

      const actionsDiv = card.querySelector('.email-actions');
      actionsDiv.appendChild(confirmationDiv);

      // Remove confirmation after 3 seconds
      setTimeout(() => {
        if (confirmationDiv.parentNode) {
          confirmationDiv.parentNode.removeChild(confirmationDiv);
        }
      }, 3000);
    }, 500);
  }

  /**
   * Toggle expand/collapse of email details
   * @param {string} index - Index of the email
   */
  toggleExpand(index) {
    const expandedDiv = document.querySelector(`.email-expanded[data-index="${index}"]`);
    const button = document.querySelector(`.toggle-expand[data-index="${index}"]`);

    if (this.expanded.has(index)) {
      // Collapse
      expandedDiv.classList.remove('show');
      expandedDiv.style.maxHeight = '0';
      button.innerHTML = '<i class="fas fa-chevron-down"></i> More';
      this.expanded.delete(index);
    } else {
      // Expand - fetch full email content if not already loaded
      if (!expandedDiv.innerHTML.trim()) {
        expandedDiv.innerHTML = '<div class="text-center py-2"><i class="fas fa-spinner fa-spin"></i> Loading...</div>';
        this.fetchEmailDetails(index).then(details => {
          expandedDiv.innerHTML = details;
        }).catch(error => {
          expandedDiv.innerHTML = '<div class="text-red-500">Error loading email details</div>';
          console.error('Error fetching email details:', error);
        });
      }

      expandedDiv.classList.add('show');
      expandedDiv.style.maxHeight = '500px';
      button.innerHTML = '<i class="fas fa-chevron-up"></i> Less';
      this.expanded.add(index);
    }
  }

  /**
   * Fetch email details
   * @param {string} index - Index of the email
   * @returns {Promise<string>} - HTML content for email details
   */
  async fetchEmailDetails(index) {
    // This would normally fetch from the server, but for now we'll just return a placeholder
    return new Promise(resolve => {
      setTimeout(() => {
        resolve(`
          <div class="email-expanded-content">
            <p>This is where the full email content would be displayed.</p>
            <p>In a real implementation, this would fetch the complete email body from the server.</p>
          </div>
        `);
      }, 500);
    });
  }

  /**
   * Mark email as read
   * @param {string} id - Email ID
   * @param {HTMLElement} button - Button element
   */
  markAsRead(id, button) {
    // Show loading state
    const originalHTML = button.innerHTML;
    button.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
    button.disabled = true;

    // Call API to mark as read
    fetch(`/api/email/${id}/read`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json'
      }
    })
    .then(response => response.json())
    .then(data => {
      if (data.success) {
        // Update UI
        const card = button.closest('.email-card');
        card.classList.remove('unread');
        button.innerHTML = '<i class="fas fa-check"></i> Marked read';
        button.disabled = true;
      } else {
        // Show error
        button.innerHTML = originalHTML;
        button.disabled = false;
        alert('Failed to mark email as read: ' + (data.message || 'Unknown error'));
      }
    })
    .catch(error => {
      console.error('Error marking email as read:', error);
      button.innerHTML = originalHTML;
      button.disabled = false;
      alert('Failed to mark email as read. Please try again.');
    });
  }

  /**
   * Open email in Gmail
   * @param {string} id - Email ID
   */
  openInGmail(id) {
    window.open(`https://mail.google.com/mail/u/0/#inbox/${id}`, '_blank');
  }

  /**
   * Format date in a human-friendly way
   * @param {Date} date - Date object
   * @returns {string} - Formatted date
   */
  formatDate(date) {
    const now = new Date();
    const diff = now - date;
    const day = 24 * 60 * 60 * 1000;
    
    // Today
    if (diff < day) {
      return date.toLocaleTimeString('en-US', {
        hour: '2-digit',
        minute: '2-digit'
      });
    }
    
    // Yesterday
    if (diff < 2 * day) {
      return 'Yesterday';
    }
    
    // This week
    if (diff < 7 * day) {
      return date.toLocaleDateString('en-US', { weekday: 'short' });
    }
    
    // Older
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric'
    });
  }
}

// Create global instance
window.emailView = new EmailView();
