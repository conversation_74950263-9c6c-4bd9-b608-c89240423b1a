/**
 * Simple test script for Gmail functionality
 * 
 * This script tests if the Gmail API is properly authenticated and can access emails.
 * Run with: node scripts/test-email-simple.js
 */

import { getUnreadEmails } from '../email.js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

async function testEmailAccess() {
  try {
    console.log('Testing Gmail API access...');
    console.log('Attempting to fetch unread emails...');
    
    const result = await getUnreadEmails(3);
    
    if (result.success) {
      console.log(`Success! Found ${result.emails.length} unread email(s).`);
      
      if (result.emails.length > 0) {
        console.log('\nEmail Preview:');
        result.emails.forEach((email, index) => {
          console.log(`\n--- Email ${index + 1} ---`);
          console.log(`From: ${email.from}`);
          console.log(`Subject: ${email.subject}`);
          console.log(`Date: ${email.date}`);
          console.log(`Snippet: ${email.snippet}`);
        });
      }
    } else {
      console.error(`Error: ${result.message}`);
    }
  } catch (error) {
    console.error('Error testing Gmail API access:', error);
  }
}

// Run the test
testEmailAccess();
