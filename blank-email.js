import { google } from 'googleapis';
import fs from 'node:fs/promises';
import path from 'node:path';
import http from 'http';
import url from 'url';
import opn from 'open'; // To automatically open the browser for authentication
import destroyer from 'server-destroy';

// Load client secrets from the downloaded credentials.json file
async function loadCredentials() {
    const credentialsPath = path.join(process.cwd(), 'credentials.json');
    const content = await fs.readFile(credentialsPath, { encoding: 'utf8' });
    return JSON.parse(content);
}

// Function to authorize and get the Gmail API client
async function authorize() {
    const credentials = await loadCredentials();
    const { client_secret, client_id, redirect_uris } = credentials.web;
    const oAuth2Client = new google.auth.OAuth2(client_id, client_secret, redirect_uris[0]);

    // Check if we have previously stored tokens
    try {
        const tokenFile = path.join(process.cwd(), 'token.json');
        const token = await fs.readFile(tokenFile, { encoding: 'utf8' });
        oAuth2Client.setCredentials(JSON.parse(token));
        return oAuth2Client;
    } catch (err) {
        return getNewToken(oAuth2Client);
    }
}

// Function to get a new access token
async function getNewToken(oAuth2Client) {
    const authUrl = oAuth2Client.generateAuthUrl({
        access_type: 'offline', // Request refresh token
        scope: ['https://www.googleapis.com/auth/gmail.readonly'], // Or other Gmail scopes
    });
    console.log('Authorize this app by visiting this url:', authUrl);

    // Create a local HTTP server to handle the callback
    const server = http.createServer(async (req, res) => {
        try {
            const parsedUrl = new url.URL(req.url, 'http://localhost:YOUR_PORT');
            const code = parsedUrl.searchParams.get('code');
            if (code) {
                console.log(`Code is ${code}`);
                const r = await oAuth2Client.getToken(code);
                oAuth2Client.setCredentials(r.tokens);
                // Store the token for future use
                await fs.writeFile(path.join(process.cwd(), 'token.json'), JSON.stringify(r.tokens));
                console.log('Token stored to token.json');
                res.end('Authentication successful! You can now close this window.');
                server.destroy();
                return;
            }
        } catch (e) {
            console.error(`Error getting token: ${e}`);
            res.writeHead(200);
            res.end('Authentication failed.');
            server.destroy();
            return;
        }
    }).listen(YOUR_PORT, () => {
        opn(authUrl, { wait: false }).then(cp => cp.unref());
    });
    destroyer(server);
    return new Promise((resolve) => server.on('close', () => resolve(oAuth2Client)));
}

// Function to list the user's emails
async function listEmails(authClient) {
    const gmail = google.gmail({ version: 'v1', auth: authClient });
    try {
        const res = await gmail.users.messages.list({
            userId: 'me', // 'me' refers to the authenticated user
            q: 'is:unread', // You can modify the query to get different emails (e.g., 'in:inbox', 'label:important')
            maxResults: 10, // Limit the number of results
        });
        const messages = res.data.messages;
        if (messages && messages.length > 0) {
            console.log('Unread emails:');
            for (const message of messages) {
                console.log(`- ${message.id}`);
                // You can now use message.id to get the full email details
                await getEmailDetails(gmail, message.id);
            }
        } else {
            console.log('No unread emails found.');
        }
    } catch (err) {
        console.error('Error listing emails:', err);
    }
}

// Function to get the details of a specific email
async function getEmailDetails(gmail, messageId) {
    try {
        const res = await gmail.users.messages.get({
            userId: 'me',
            id: messageId,
            format: 'full', // 'full' gets the raw message data
        });
        const message = res.data;
        // Process the email details here
        console.log('Email Details:', message);

        // To get the raw email content (for mailparser):
        const rawEmail = Buffer.from(message.raw, 'base64').toString('utf-8');
        // Now you can pass 'rawEmail' to your mailparser library
        // const parsedEmail = await simpleParser(rawEmail);
        // console.log('Parsed Email:', parsedEmail);

    } catch (err) {
        console.error('Error getting email details:', err);
    }
}

// Main function to run the process
async function main() {
    const authClient = await authorize();
    if (authClient) {
        await listEmails(authClient);
    }
}

// Replace YOUR_PORT with the port you configured in your OAuth credentials
const YOUR_PORT = 3000;
main();