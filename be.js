import fs from "fs";
import { google } from "googleapis";
import path from 'path';
import { fileURLToPath } from 'url';

// Get the directory name
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Path to token file
const TOKEN_PATH = path.join(__dirname, 'token.json'); // Simplified path

// Initialize the OAuth client using token.json file
function getOAuthClient() {
    // Check if token file exists
    if (!fs.existsSync(TOKEN_PATH)) {
        throw new Error("Token file not found. Please authenticate first.");
    }

    // Read token data
    const tokenData = JSON.parse(fs.readFileSync(TOKEN_PATH));

    // Create OAuth client with the actual credentials
    const oAuth2Client = new google.auth.OAuth2(
        '122591791863-e7o6l17e9k3i4e6kogr96t99hdffvkma.apps.googleusercontent.com',   // Client ID
        '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',                                           // Client Secret
        'http://localhost:3081/oauth2callback'                                           // Redirect URI
    );

    // Set the credentials from the token file
    oAuth2Client.setCredentials(tokenData);

    return oAuth2Client;
}

/**
 * Fetch unread emails from Gmail (Simplified)
 * @param {number} maxResults - Maximum number of emails to fetch
 * @returns {Array} - Array of email objects
 */
async function getUnreadEmails(maxResults = 5) {
    try {
        const auth = getOAuthClient();
        const gmail = google.gmail({ version: "v1", auth });

        const res = await gmail.users.messages.list({
            userId: "me",
            labelIds: ["INBOX"],
            q: "is:unread",
            maxResults: maxResults,
        });

        if (!res.data.messages) {
            return []; // Return empty array if no unread emails
        }

        const emails = [];
        for (const msg of res.data.messages) {
            const email = await gmail.users.messages.get({ userId: "me", id: msg.id });
            const from = getHeader(email.data.payload.headers, "From");
            const subject = getHeader(email.data.payload.headers, "Subject");
            const date = getHeader(email.data.payload.headers, "Date");
            const snippet = email.data.snippet || "";

            emails.push({
                id: msg.id,
                from,
                subject,
                date,
                snippet,
                unread: email.data.labelIds.includes("UNREAD")
            });
        }

        return emails;
    } catch (error) {
        console.error("Error fetching emails:", error);
        return []; // Return empty array on error
    }
}

// Helper function to get header value
function getHeader(headers, name) {
    const header = headers.find((h) => h.name.toLowerCase() === name.toLowerCase());
    return header ? header.value : "N/A";
}

// Example usage
async function main() {
    try {
        const emails = await getUnreadEmails(10);
        if (emails.length > 0) {
            console.log("Unread Emails:");
            emails.forEach(email => {
                console.log(`  - From: ${email.from}, Subject: ${email.subject}`);
            });
        } else {
            console.log("No unread emails found.");
        }
    } catch (error) {
        console.error("Error in main:", error);
    }
}

main();