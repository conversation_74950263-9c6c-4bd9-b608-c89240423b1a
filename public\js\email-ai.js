/**
 * EmailAI class for intelligent email processing
 */
class EmailAI {
  constructor() {
    this.categories = {
      FINANCE: ['bank', 'payment', 'invoice', 'credit', 'transaction', 'balance'],
      TRAVEL: ['flight', 'booking', 'reservation', 'hotel', 'itinerary', 'travel'],
      SHOPPING: ['order', 'shipping', 'delivery', 'purchase', 'track', 'amazon'],
      SOCIAL: ['invitation', 'event', 'birthday', 'party', 'celebration'],
      WORK: ['meeting', 'project', 'deadline', 'report', 'client', 'team']
    };
  }

  /**
   * Process emails with AI features
   * @param {Array} emails - Array of email objects
   * @returns {Object} - Processed data with emails, clusters, and stats
   */
  processEmails(emails) {
    if (!emails || !emails.length) return { emails: [] };

    try {
      // Add priority if not already present
      const processedEmails = emails.map(email => {
        if (!email.priority) {
          const priority = this.determinePriority(email);
          return {
            ...email,
            priority,
            priorityScore: priority === 'high' ? 80 : priority === 'medium' ? 60 : 30,
            category: this.categorizeEmail(email),
            priorityReasons: this.getPriorityReasons(email, priority)
          };
        }
        return email;
      });

      // Create clusters
      const clusters = this.clusterEmails(processedEmails);

      return {
        emails: processedEmails,
        clusters,
        stats: this.generateStats(processedEmails)
      };
    } catch (error) {
      console.error('Error in AI processing:', error);
      return { emails };
    }
  }

  /**
   * Determine email priority based on content and metadata
   * @param {Object} email - Email object
   * @returns {string} - Priority level (high, medium, low)
   */
  determinePriority(email) {
    const subject = (email.subject || '').toLowerCase();
    const sender = (email.from || '').toLowerCase();
    const body = (email.snippet || '').toLowerCase();
    
    // High priority keywords
    const highPriorityTerms = ['urgent', 'important', 'action required', 'immediately', 
                              'asap', 'deadline', 'overdue', 'alert', 'critical'];
    
    // Check for high priority indicators
    for (const term of highPriorityTerms) {
      if (subject.includes(term) || body.includes(term)) {
        return 'high';
      }
    }
    
    // Medium priority for financial emails
    const financialTerms = ['payment', 'invoice', 'bill', 'transaction', 'account', 'balance'];
    for (const term of financialTerms) {
      if (subject.includes(term) || body.includes(term)) {
        return 'medium';
      }
    }
    
    // Default to low priority
    return 'low';
  }

  /**
   * Categorize email based on content
   * @param {Object} email - Email object
   * @returns {string} - Category
   */
  categorizeEmail(email) {
    const text = `${email.subject || ''} ${email.snippet || ''}`.toLowerCase();
    
    for (const [category, keywords] of Object.entries(this.categories)) {
      for (const keyword of keywords) {
        if (text.includes(keyword)) {
          return category;
        }
      }
    }
    
    return 'OTHER';
  }
}

// Initialize and export
window.emailAI = new EmailAI();

