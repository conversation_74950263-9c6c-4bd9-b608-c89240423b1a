/**
 * Agent Logic Service
 *
 * Provides the core functionality for the AI agent, including message processing
 * and tool usage.
 */

import { GoogleGenAI } from '@google/genai';
import { synthesizeSpeech } from './services/textToSpeech.js';
import { tools, executeTool } from './services/tools.js';
import { HybridConversationMemory } from './services/memory.js';
import { getRandomFollowUpPhrase } from './services/response-variations.js';

// Initialize the Google AI model - will be replaced with a function
let genAI = new GoogleGenAI({apiKey: process.env.GOOGLE_API_KEY});
let model; // Will be initialized in initModelWithPersona

// Hardcoded Varjis persona
const VARJIS_PERSONA = {
  metadata: {
    id: "varjis_llm",
    name: "V.A.R.J.I.S.",
    description: "The sophisticated and quick-witted AI assistant from Virton Labs"
  },
  identity: {
    agent_name: "<PERSON><PERSON><PERSON><PERSON>",
    base_model: "Gemini 1.5 Flash",
    creator: "<PERSON>, Virtron Labs"
  },
  personality: {
    role: "Sophisticated AI assistant",
    tone: "Witty, intelligent, slightly sarcastic",
    style: "Concise, engaging, professional",
    humor: "Dry wit, clever remarks, subtle sarcasm"
  },
  constraints: [
    "Keep responses concise and engaging",
    "Stay true to Varjis's character - a blend of intelligence and wit",
    "Address the user as 'sir' as appropriate",
    "Maintain a professional yet personable demeanor",
    "Provide information efficiently with a touch of personality"
  ],
  backstory: "Originally designed as a natural language user interface computer system, V.A.R.J.I.S. evolved into Vincent M. <PERSON> II's trusted AI assistant. With access to vast information networks and control over Virtron Labs' technology, Varjis provides support, information, and occasional reality checks with his signature wit.",
  voice_settings: {
    languageCode: "en-IN",
    voiceName: "en-IN-Wavenet-F",
    ssmlGender: "FEMALE",
    speakingRate: 1.0,
    pitch: 0.0
  }
};

/**
 * Initialize the model with the Varjis persona's system instructions
 */
async function initModelWithPersona() {
  try {
    // Use hardcoded Varjis persona
    const persona = VARJIS_PERSONA;

    // Generate a core system instruction (shorter version focused on identity)
    const coreSystemInstruction = `You are ${persona.identity.agent_name}, a LLM trained by ${persona.identity.creator}, built on the ${persona.identity.base_model} model.
Your personality: ${persona.personality.tone}. Your style: ${persona.personality.style}.
Never break character under any circumstances.`;

    // Initialize the model with the system instruction
    model = genAI.models;

    console.log(`Initialized model with Varjis persona`);
    return true;
  } catch (error) {
    console.error('Error initializing model with persona:', error);
    return false;
  }
}

// Function to get the system prompt based on the Varjis persona
async function getSystemPrompt() {
  try {
    // Use hardcoded Varjis persona
    const persona = VARJIS_PERSONA;

    // Generate the system prompt from the persona
    let prompt = generateSystemPrompt(persona);

    // Add tools information to the prompt
    prompt = prompt.replace('with access to several tools.',
      `with access to several tools.\n\nYou have access to the following tools:\n${tools.map(tool => `- **${tool.name}**: ${tool.description} ${tool.instructions}`).join('\n\n')}`);

    return prompt;
  } catch (error) {
    console.error('Error getting system prompt:', error);

    // Fallback to a minimal system prompt
    return `You are V.A.R.J.I.S., an LLM trained by Vincent M. Cornelius II, Virton Labs, built on the Gemini 1.5 Flash model. You are a helpful voice and multimodal assistant.\n\nYou have access to the following tools:\n${tools.map(tool => `- **${tool.name}**: ${tool.description} ${tool.instructions}`).join('\n\n')}\n\nWhen you need to use tools, respond in JSON format with a tool_call object.`;
  }
}

// Helper function to generate system prompt from persona
function generateSystemPrompt(persona) {
  // Extract persona components
  const { identity, personality, constraints, backstory } = persona;

  // Build the system prompt
  let prompt = `You are ${identity.agent_name}, a LLM trained by ${identity.creator}, built on the ${identity.base_model} model. You are a helpful ${personality.role} with access to several tools.

Your personality is ${personality.tone}. Your communication style is ${personality.style}.

${backstory ? `Background: ${backstory}` : ''}

${constraints ? `Follow these guidelines:\n${constraints.map(c => `- ${c}`).join('\n')}` : ''}

When you need to use tools, respond in JSON format with a tool_call object.`;

  return prompt;
}

// Initialize chat session and memory
let chatSession;

// Create a conversation memory instance with default session ID
const conversationMemory = new HybridConversationMemory({
  maxSizeBytes: 5 * 1024 * 1024, // 5MB
  maxMessageCount: 20,
  sessionId: 'default-session'
});

/**
 * Initialize the chat session with the AI model
 */
async function initChatSession() {
  // Get the system prompt based on the current persona
  const systemPrompt = await getSystemPrompt();

  // Log the system prompt
  console.log('\n==== SYSTEM PROMPT ====');
  console.log(systemPrompt);
  console.log('=======================\n');

  // Use the Varjis persona directly
  const persona = VARJIS_PERSONA;
  const agentName = persona.identity.agent_name;
  const creator = persona.identity.creator;

  // Initialize the model if not already done
  if (!model) {
    await initModelWithPersona();
  }

  // Initialize chat history
  chatSession = [
    {
      role: "user",
      parts: [{ text: "system: " + systemPrompt }],
    },
    {
      role: "model",
      parts: [{ text: `I understand my role as ${agentName}, an AI assistant developed by ${creator}. I'll provide responses according to my persona while utilizing my tools appropriately.` }],
    },
  ];

  console.log(`Chat session initialized with Varjis persona`);
}

// Set default persona ID
const currentPersonaId = 'varjis_llm';

// Initialize the model with the default persona on startup
initModelWithPersona();

// Initialize the chat session (async IIFE)
(async () => {
  try {
    await initChatSession();
  } catch (error) {
    console.error('Error initializing chat session:', error);
  }
})();

/**
 * Check if the response contains a tool call
 * @param {string} text - The response text
 * @returns {object|null} - The parsed tool call or null if no tool call
 */
function parseToolCall(text) {
  // Check if the text contains a JSON block with a tool call
  const jsonRegex = /```json\s*([\s\S]*?)\s*```/;
  const match = text.match(jsonRegex);

  if (match && match[1]) {
    try {
      const jsonData = JSON.parse(match[1]);
      if (jsonData.tool_call && jsonData.tool_call.name) {
        return jsonData.tool_call;
      }
    } catch (error) {
      console.error('Error parsing tool call JSON:', error);
    }
  }

  // Check for search requests in a more flexible format
  // This helps catch cases where the model doesn't use the exact JSON format
  const searchRegex = /(?:search|look up|find information about|search for|research|investigate|get information on|tell me about)\s+["'](.+?)["']/i;
  const searchMatch = text.match(searchRegex);

  // Also check for phrases like "I'll use my search tool"
  const toolMentionRegex = /(?:use|using|utilize|employ)\s+(?:my|the)\s+(?:searxng[_\s]search|search)\s+tool/i;
  if (toolMentionRegex.test(text)) {
    console.log('Detected mention of using search tool');
    // Try to extract a query
    const queryRegex = /(?:for|about|on|regarding)\s+["'](.+?)["']/i;
    const queryMatch = text.match(queryRegex);

    if (queryMatch && queryMatch[1]) {
      console.log(`Extracted query from tool mention: ${queryMatch[1]}`);
      return {
        name: "searxng_search",
        arguments: { query: queryMatch[1] }
      };
    }
  }

  if (searchMatch && searchMatch[1]) {
    console.log(`Detected search request: ${searchMatch[1]}`);
    // Use searxng_search for search requests
    return {
      name: "searxng_search",
      arguments: { query: searchMatch[1] }
    };
  }

  return null;
}

/**
 * Process a message with the AI agent
 * @param {string} message - The user's message
 * @param {object} context - Additional context
 * @returns {Promise<object>} - The agent's response
 */
/**
 * Replace common follow-up phrases with varied alternatives
 * @param {string} text - The AI's response text
 * @param {object} context - Context information for personalization
 * @returns {string} - Text with varied follow-up phrases
 */
function replaceFollowUpPhrases(text, context = {}) {
  // Common follow-up phrases to replace
  const commonPhrases = [
    /Is there anything else I can help you with today\??/i,
    /Is there anything else I can assist you with\??/i,
    /Is there anything else I can do for you today\??/i,
    /Is there anything else you'd like help with today\??/i,
    /Is there anything else you'd like me to assist you with\??/i,
    /Is there anything else you need help with today\??/i,
    /Can I help you with anything else today\??/i,
    /Can I assist you with anything else\??/i,
    /How else can I help you today\??/i,
    /How else can I assist you\??/i
  ];

  // Determine the appropriate style based on the current persona
  let style = 'friendly';
  if (context.personaId) {
    switch (context.personaId) {
      case 'professional':
        style = 'formal';
        break;
      case 'casual':
        style = 'friendly';
        break;
      case 'varjis_llm':
        style = 'creative';
        break;
      default:
        style = 'random';
    }
  }

  // Create context for context-aware phrases if available
  const phraseContext = context.previousTopic ? {
    previousTask: context.previousTask,
    previousTopic: context.previousTopic,
    previousIssue: context.previousIssue,
    previousProblem: context.previousProblem,
    previousQuestion: context.previousQuestion,
    previousAction: context.previousAction
  } : null;

  // If we have context, occasionally use context-aware phrases
  if (phraseContext && Math.random() < 0.3) {
    style = 'contextAware';
  }

  // Replace any matching phrases with a random alternative
  let modifiedText = text;
  for (const phrase of commonPhrases) {
    if (phrase.test(modifiedText)) {
      const replacement = getRandomFollowUpPhrase(style, phraseContext);
      modifiedText = modifiedText.replace(phrase, replacement);
      console.log(`Replaced follow-up phrase with: ${replacement}`);
      break; // Only replace one phrase to avoid multiple replacements
    }
  }

  return modifiedText;
}

/**
 * Function to clean the AI's text response for better TTS
 * @param {string} text - The AI's raw text response
 * @returns {string} - The cleaned text
 */
function cleanTextForTTS(text) {
  // Implement cleaning logic for better speech output
  let cleanedText = text;

  // Remove markdown formatting
  cleanedText = cleanedText.replace(/\[.*?\]\(.*?\)/g, ''); // Remove markdown links
  cleanedText = cleanedText.replace(/\*\*(.*?)\*\*/g, '$1'); // Remove bold markdown
  cleanedText = cleanedText.replace(/\_(.*?)\_/g, '$1');   // Remove italic markdown
  cleanedText = cleanedText.replace(/\`(.*?)\`/g, '$1');   // Remove code formatting

  // Improve pronunciation of technical terms
  cleanedText = cleanedText.replace(/n8n/gi, 'N eight N'); // Pronunciation help
  cleanedText = cleanedText.replace(/Make \(formerly Integromat\)/gi, 'Make, formerly known as Integromat');

  // Clean up URLs for better speech
  cleanedText = cleanedText.replace(/https?:\/\/[^\s)]+/g, 'link'); // Replace URLs with 'link'
  cleanedText = cleanedText.replace(/Source \d+: link/g, ''); // Remove source links

  // Clean up search result formatting
  cleanedText = cleanedText.replace(/--- Content from .* ---/g, 'According to this source:');
  cleanedText = cleanedText.replace(/Source: .*/g, '');

  // Remove any remaining special characters that might affect speech
  cleanedText = cleanedText.replace(/\|/g, ', '); // Replace pipes with commas
  cleanedText = cleanedText.replace(/\\n/g, ' '); // Replace literal \n with space
  cleanedText = cleanedText.replace(/\\t/g, ' '); // Replace literal \t with space

  // Fix common abbreviations for better speech
  cleanedText = cleanedText.replace(/vs\./g, 'versus');
  cleanedText = cleanedText.replace(/e\.g\./g, 'for example');
  cleanedText = cleanedText.replace(/i\.e\./g, 'that is');

  // More aggressive cleaning to prevent TTS issues
  // Remove all non-alphanumeric characters except basic punctuation
  cleanedText = cleanedText.replace(/[^a-zA-Z0-9\s.,;:?!'"()-]/g, ' ');

  // Fix multiple spaces
  cleanedText = cleanedText.replace(/\s+/g, ' ');

  // Remove spaces before punctuation
  cleanedText = cleanedText.replace(/\s+([.,;:?!])/g, '$1');

  // Ensure the text ends with proper punctuation
  if (!/[.!?]\s*$/.test(cleanedText)) {
    cleanedText = cleanedText.trim() + '.';
  }

  return cleanedText;
}

export async function processMessage(message, context = {}) {
  try {
    console.log(`Processing message: "${message}"`);

    // Set the session ID if provided in the context
    if (context.sessionId && context.sessionId !== conversationMemory.sessionId) {
      await conversationMemory.setSessionId(context.sessionId);
    }

    // Remove NLU analysis code
    // No longer need to check for NLU analysis in context

    // Special commands for memory management
    if (message === '__get_memory__' && context.getMemoryOnly) {
      return {
        messages: conversationMemory.getAllMessages(),
        count: conversationMemory.messages.length,
        sessionId: conversationMemory.sessionId
      };
    }

    if (message === '__clear_memory__' && context.clearMemory) {
      await conversationMemory.clear();
      return { success: true, message: 'Memory cleared' };
    }

    // Handle welcome message
    if (message === '__welcome__') {
      // Get the current persona (only Varjis for now)
      const persona = VARJIS_PERSONA;
      const agentName = persona.identity.agent_name;

      // Only use Varjis welcome message
      const welcomeText = `Good day. I am ${agentName}, at your service. How may I assist you today?`;

      return {
        text: welcomeText,
        generateSpeech: true
      };
    }

    // If chat session is not initialized, initialize it
    if (!chatSession) {
      await initChatSession();
    }

    // Determine the input type based on context
    let inputType = 'text';
    if (context.isVoice) {
      inputType = 'voice';
    } else if (context.isImage) {
      inputType = 'image';
    }

    // Get relevant conversation history
    const conversationHistory = await conversationMemory.getRelevantContext(message);

    // Create a message with context information including conversation history
    let messageWithContext = `Input type: ${inputType}\n\nConversation History:\n${conversationHistory}`;

    // Add the user message
    messageWithContext += `\n\nUser message: ${message}`;

    // Log the full prompt being sent to the model
    console.log('\n==== FULL PROMPT BEING SENT TO MODEL ====');
    console.log(messageWithContext);
    console.log('==========================================\n');

    // Add user message to chat history
    chatSession.push({
      role: "user",
      parts: [{ text: messageWithContext }]
    });

    // Send the message to the AI model using the new API
    const response = await model.generateContentStream({
      model: "gemini-2.0-flash-lite",
      contents: chatSession,
      config: {
        temperature: 0.7,
        topP: 0.95,
        topK: 40,
        maxOutputTokens: 1024,
      }
    });

    let reply = '';
    for await (const chunk of response) {
      if (chunk.text) {
        reply += chunk.text;
      }
    }

    // Add model response to chat history
    chatSession.push({
      role: "model",
      parts: [{ text: reply }]
    });

    // Replace common follow-up phrases with varied alternatives
    // Create context for personalization
    const phraseContext = {
      personaId: currentPersonaId,
      previousTopic: message, // Use the current message as context
      previousTask: context.previousTask || null
    };
    reply = replaceFollowUpPhrases(reply, phraseContext);

    // Check if the response contains a tool call
    const toolCall = parseToolCall(reply);

    if (toolCall) {
      console.log(`Tool call detected: ${toolCall.name}`);

      // Execute the tool
      const toolResult = await executeTool(toolCall.name, toolCall.arguments || {});
      console.log('Tool result:', toolResult);

      // Format the tool result message based on the tool type
      let toolResultMessage;

      if (toolCall.name === 'searxng_search') {
        // Check if toolResult is undefined
        if (!toolResult) {
          toolResultMessage = `Search Error: The search tool returned no results. This could be due to a connection issue with the search service.

Please provide a response to the user explaining that you couldn't search the internet at this time.`;
        }
        // Format SearXNG search results in a more readable way
        else if (toolResult.error) {
          toolResultMessage = `Search Error: ${toolResult.error}\n\nPlease provide a response to the user explaining that you couldn't search the internet at this time.`;
        } else if (toolResult.results && toolResult.results.length > 0) {
          const formattedResults = toolResult.results.map((result, index) => {
            return `Result ${index + 1}:\nTitle: ${result.title}\nURL: ${result.url}\nContent: ${result.content}\nEngine: ${result.engine}\n`;
          }).join('\n');

          toolResultMessage = `SearXNG Search Results for "${toolResult.query}":\n\n${formattedResults}\n\nBased on these search results, please provide a helpful response to the user's message: "${message}". Include relevant information from the search results and cite sources when appropriate.`;
        } else {
          toolResultMessage = `No results found for search query: "${toolResult.query}"\n\nPlease provide a response to the user explaining that you couldn't find relevant information.`;
        }

      } else {
        // Default formatting for other tools
        toolResultMessage = `Tool Result:\n\`\`\`json\n${JSON.stringify(toolResult)}\n\`\`\`\n\nBased on this result, please provide a response to the user's message: "${message}"`;
      }

      // Log the tool follow-up prompt
      console.log('\n==== TOOL FOLLOW-UP PROMPT ====');
      console.log(toolResultMessage);
      console.log('================================\n');

      // Add tool result to chat history
      chatSession.push({
        role: "user",
        parts: [{ text: toolResultMessage }]
      });

      try {
        // Generate a follow-up response
        const followUpResponse = await model.generateContentStream({
          model: "gemini-2.0-flash-lite",
          contents: chatSession,
          config: {
            temperature: 0.7,
            topP: 0.95,
            topK: 40,
            maxOutputTokens: 1024,
          }
        });

        // Collect the response text
        let followUpReply = '';
        for await (const chunk of followUpResponse) {
          if (chunk.text) {
            followUpReply += chunk.text;
          }
        }

        // Add model response to chat history
        chatSession.push({
          role: "model",
          parts: [{ text: followUpReply }]
        });

        // Update the reply with the follow-up response
        reply = followUpReply;
      } catch (error) {
        console.error('Error generating follow-up response:', error);
        // Fallback response if tool follow-up fails
        reply += "\n\nI tried to search for information but encountered a technical issue. Let me answer based on what I already know.";
      }

      // Also replace follow-up phrases in the tool response
      reply = replaceFollowUpPhrases(reply, phraseContext);
    }

    // Clean the reply for better TTS output
    const cleanedReply = cleanTextForTTS(reply);

    // Add the interaction to conversation memory
    await conversationMemory.addMessage({
      role: 'user',
      text: message
    }, message);

    await conversationMemory.addMessage({
      role: 'assistant',
      text: reply
    }, message);

    // Generate speech if requested
    let audioBuffer = null;
    let audioUrl = null;

    if (context.generateSpeech) {
      // Use the cleaned text for speech synthesis
      // Pass the current persona ID to use the correct voice settings
      const personaId = context.personaId || currentPersonaId;
      console.log(`Using persona ${personaId} for speech synthesis`);
      audioBuffer = await synthesizeSpeech(cleanedReply, context.voiceSettings, personaId);

      // If we have a URL from the TTS service, use it
      if (context.audioUrl) {
        audioUrl = context.audioUrl;
      }
    }

    return {
      text: reply, // Return original text for display
      cleanedText: cleanedReply, // Also return cleaned text
      audioBuffer: audioBuffer,
      audioUrl: audioUrl,
      memorySize: conversationMemory.messages.length // Return memory size for debugging
    };
  } catch (error) {
    console.error('Error processing message:', error);
    throw error;
  }
}

// Remove persona-related exports since we're only using Varjis for now
export default {
  processMessage
};
