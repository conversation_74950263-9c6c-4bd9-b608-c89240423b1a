/**
 * NLU Service
 *
 * Provides natural language understanding capabilities using Google's Generative AI
 */

import { GoogleGenerativeAI } from "@google/generative-ai";
import dotenv from 'dotenv';

dotenv.config();

// Configuration for the NLU model
const llmNluConfig = {
  modelName: "gemini-1.5-flash", 
  generationConfig: {
    temperature: 0.3, // Lower temperature for more deterministic NLU output
    topK: 20,
    topP: 0.8,
    maxOutputTokens: 512, // Increased for potential explanations
  },
};

// Initialize the Google AI model for NLU
const genAI_nlu = new GoogleGenerativeAI(process.env.GOOGLE_API_KEY);
const nluModel = genAI_nlu.getGenerativeModel({
  model: llmNluConfig.modelName,
  generationConfig: llmNluConfig.generationConfig,
});

// Define custom intents - expand this list as needed
const customIntents = [
  // Original intents
  "book_flight",
  "get_weather",
  "play_music",
  "set_reminder",
  "get_news",
  "query_time",
  "translate",
  "express_positive_emotion",
  "express_dissatisfaction",
  "provide_feedback",
  "set_alarm",
  "request_information",
  "make_a_reservation",
  "order_food",

  // Email-related intents
  "check_email",
  "send_email",
  "summarize_emails",
  "filter_emails",

  // Web-related intents
  "search_web",
  "open_website",
  "get_directions",

  // Utility intents
  "calculate",
  "convert_units",
  "define_term",

  // Device control intents
  "adjust_volume",
  "change_settings",

  // Personal assistant intents
  "tell_joke",
  "get_recommendations",
  "create_list",
  "add_to_list",

  // Conversation flow intents
  "greeting",
  "farewell",
  "thank_you",
  "repeat_information",
  "clarify_information",

  // Fallback intent
  "unknown"
];

// Construct the intent instructions string for the prompt
const intentInstructions = `The possible intents are: ${customIntents.join(", ")}.`;

// The prompt template
const nluPromptTemplate = `You are a detailed linguistic analysis engine. Your task is to read the user's message and accurately determine its sentiment, the primary intent (from the provided list), and identify any relevant named entities with as much specificity as possible. Return your analysis as a JSON object with the following keys:
- "sentiment": The overall sentiment of the message ("positive", "negative", "neutral", or "mixed").
- "intent": A concise label describing the user's primary goal or request from the following list: ${intentInstructions}. If the intent is unclear, use "unknown".
- "entities": An object where keys are the entity types (e.g., "location", "date", "product", "person", "team") and values are arrays of the identified entities. Be specific (e.g., "Winter Haven, Florida").
- "confidence": An object containing your confidence score (between 0 and 1) for each of "sentiment", "intent", and "entities".
- "explanation": A brief explanation for the confidence score assigned to each of "sentiment", "intent", and "entities".
Consider the current context: Current location is Winter Haven, Florida, United States; Current time is {{current_time}}.
Analyze the following user message:
""" {{user_message}} """
Respond ONLY with a valid JSON object.`;

/**
 * Analyze a user message using the NLU engine
 * @param {string} userMessage - The message to analyze
 * @returns {Object|null} - The analysis result or null if analysis failed
 */
export async function analyze(userMessage) {
  // Get current time for context
  const now = new Date();
  const timeString = now.toLocaleString('en-US', {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: 'numeric',
    minute: 'numeric',
    timeZoneName: 'short'
  });

  // Replace placeholders in the prompt template
  const prompt = nluPromptTemplate
    .replace("{{user_message}}", userMessage)
    .replace("{{current_time}}", timeString);

  try {
    const result = await nluModel.generateContent([prompt]);
    const responseText = result.response?.candidates?.[0]?.content?.parts?.[0]?.text;

    if (responseText) {
      let jsonString = responseText.trim();

      // Remove Markdown-style code block formatting like ```json ... ```
      if (jsonString.startsWith("```")) {
        jsonString = jsonString.replace(/^```(?:json)?/, '').replace(/```$/, '').trim();
      }

      try {
        const nluResult = JSON.parse(jsonString);
        return nluResult;
      } catch (error) {
        console.error("Primary JSON parse failed. Attempting fallback extraction.");
        // Try extracting JSON block between first and last curly brace
        const firstBrace = jsonString.indexOf('{');
        const lastBrace = jsonString.lastIndexOf('}');
        if (firstBrace !== -1 && lastBrace !== -1) {
          const fallback = jsonString.substring(firstBrace, lastBrace + 1);
          try {
            return JSON.parse(fallback);
          } catch (fallbackError) {
            console.error("Fallback parse also failed:", fallbackError, fallback);
            return null;
          }
        }
        return null;
      }
    } else {
      console.warn("Gemini NLU returned an empty response.");
      return null;
    }
  } catch (error) {
    console.error("Error calling Gemini for NLU:", error);
    return null;
  }
}
