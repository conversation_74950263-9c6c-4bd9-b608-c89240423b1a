/**
 * Test script for the email tools
 * 
 * Run with: node scripts/test-email-tools.js [tool] [args]
 * 
 * Examples:
 * - node scripts/test-email-tools.js unread
 * - node scripts/test-email-tools.js search "from:<EMAIL>"
 * - node scripts/test-email-tools.js read MESSAGE_ID
 * - node scripts/test-email-tools.js mark-read MESSAGE_ID
 */

import { executeTool } from '../services/tools.js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

async function testEmailTools() {
  try {
    // Get the command line arguments
    const command = process.argv[2] || 'unread';
    const arg = process.argv[3] || '';
    
    console.log(`Testing email tool: ${command}`);
    
    let result;
    
    switch (command) {
      case 'unread':
        // Test getting unread emails
        const limit = arg ? parseInt(arg) : 5;
        result = await executeTool('email_unread', { limit });
        break;
        
      case 'search':
        // Test searching emails
        if (!arg) {
          console.error('Error: Search query is required');
          process.exit(1);
        }
        result = await executeTool('email_search', { query: arg });
        break;
        
      case 'read':
        // Test reading a specific email
        if (!arg) {
          console.error('Error: Email ID is required');
          process.exit(1);
        }
        result = await executeTool('email_read', { id: arg });
        break;
        
      case 'mark-read':
        // Test marking an email as read
        if (!arg) {
          console.error('Error: Email ID is required');
          process.exit(1);
        }
        result = await executeTool('email_mark_read', { id: arg });
        break;
        
      default:
        console.error(`Error: Unknown command "${command}"`);
        console.log('Available commands: unread, search, read, mark-read');
        process.exit(1);
    }
    
    // Print the result
    console.log('\nResult:');
    console.log(JSON.stringify(result, null, 2));
    
    // Print a summary based on the command
    console.log('\nSummary:');
    
    if (command === 'unread' || command === 'search') {
      if (result.success) {
        console.log(`Found ${result.emails.length} email(s)`);
        
        if (result.emails.length > 0) {
          console.log('\nEmail Preview:');
          result.emails.forEach((email, index) => {
            console.log(`\n--- Email ${index + 1} ---`);
            console.log(`From: ${email.from}`);
            console.log(`Subject: ${email.subject}`);
            console.log(`Date: ${email.date}`);
            console.log(`Snippet: ${email.snippet}`);
            console.log(`ID: ${email.id}`);
          });
        }
      } else {
        console.log(`Error: ${result.message}`);
      }
    } else if (command === 'read') {
      if (result.success && result.email) {
        console.log(`Email Details:`);
        console.log(`From: ${result.email.from}`);
        console.log(`To: ${result.email.to}`);
        console.log(`Subject: ${result.email.subject}`);
        console.log(`Date: ${result.email.date}`);
        console.log(`\nBody Preview (first 200 chars):`);
        console.log(result.email.body.substring(0, 200) + '...');
      } else {
        console.log(`Error: ${result.message}`);
      }
    } else if (command === 'mark-read') {
      console.log(result.message);
    }
    
  } catch (error) {
    console.error('Error testing email tools:', error);
  }
}

// Run the test
testEmailTools();
