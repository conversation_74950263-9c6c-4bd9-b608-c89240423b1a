/**
 * Simple script to mark an email as read by subject or sender
 * 
 * Run with: node scripts/mark-email-read.js
 * 
 * This script will:
 * 1. List your unread emails
 * 2. Ask you which one to mark as read
 * 3. Mark it as read
 * 4. Confirm the action
 */

import { getUnreadEmails, markAsRead } from '../email.js';
import readline from 'readline';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Create readline interface
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

async function markEmailReadDemo() {
  try {
    console.log("Fetching your unread emails...");
    
    // Get unread emails
    const result = await getUnreadEmails(10);
    
    if (!result.success || result.emails.length === 0) {
      console.log("No unread emails found.");
      rl.close();
      return;
    }
    
    // Display emails
    console.log("\nYour unread emails:");
    result.emails.forEach((email, index) => {
      console.log(`${index + 1}. From: ${email.from}`);
      console.log(`   Subject: ${email.subject}`);
      console.log(`   Date: ${email.date}`);
      console.log(`   ID: ${email.id}`);
      console.log();
    });
    
    // Ask which email to mark as read
    rl.question("\nEnter the number of the email you want to mark as read: ", async (answer) => {
      const index = parseInt(answer) - 1;
      
      if (isNaN(index) || index < 0 || index >= result.emails.length) {
        console.log("Invalid selection.");
        rl.close();
        return;
      }
      
      const selectedEmail = result.emails[index];
      console.log(`\nMarking email as read: "${selectedEmail.subject}" from ${selectedEmail.from}`);
      
      // Mark as read
      const markResult = await markAsRead(selectedEmail.id);
      
      if (markResult.success) {
        console.log("\nSuccess! Email marked as read.");
      } else {
        console.log(`\nError: ${markResult.message}`);
      }
      
      rl.close();
    });
  } catch (error) {
    console.error("Error:", error);
    rl.close();
  }
}

// Run the demo
markEmailReadDemo();