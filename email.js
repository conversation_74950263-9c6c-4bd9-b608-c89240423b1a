import fs from "fs";
import { google } from "googleapis";
import path from 'path';
import { fileURLToPath } from 'url';

// Get the directory name
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Path to token file
const TOKEN_PATH = path.join(__dirname, 'config', 'token.json');

// Make sure the config directory exists
fs.mkdirSync(path.dirname(TOKEN_PATH), { recursive: true });

// Export SCOPES for use in other modules (like test-gmail-auth.js for initial authentication)
export const SCOPES = [
  "https://www.googleapis.com/auth/gmail.readonly",
  "https://www.googleapis.com/auth/gmail.modify"
];

// Initialize the OAuth client using token.json file
export function getOAuthClient() {
  // Check if token file exists
  if (!fs.existsSync(TOKEN_PATH)) {
    throw new Error("Token file not found. Please run the scripts/test-gmail-auth.js file first to authenticate.");
  }

  // Read token data
  const tokenData = JSON.parse(fs.readFileSync(TOKEN_PATH));

  // Create OAuth client with the actual credentials
  const oAuth2Client = new google.auth.OAuth2(
    '122591791863-e7o6l17e9k3i4e6kogr96t99hdffvkma.apps.googleusercontent.com',  // Client ID
    '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',                                     // Client Secret
    'http://localhost:3081/oauth2callback'                                      // Redirect URI
  );

  // Set the credentials from the token file
  oAuth2Client.setCredentials(tokenData);

  return oAuth2Client;
}

/**
 * Fetch unread emails from Gmail
 * @param {number} maxResults - Maximum number of emails to fetch
 * @param {boolean} count_only - Whether to only return the count of unread emails
 * @param {boolean} categorize - Whether to categorize emails by importance
 * @returns {Object} - Object containing success status, emails array, and message
 */
export async function getUnreadEmails(maxResults = 5, count_only = false, categorize = false) {
  try {
    const auth = getOAuthClient();
    const gmail = google.gmail({ version: "v1", auth });

    // Use a higher limit when we need all emails for counting or categorizing
    const limit = count_only || categorize ? 100 : maxResults;

    const res = await gmail.users.messages.list({
      userId: "me",
      labelIds: ["INBOX"],
      q: "is:unread",
      maxResults: limit,
    });

    if (!res.data.messages) {
      return {
        success: true,
        emails: [],
        count: 0,
        message: "No unread emails found."
      };
    }

    // If we only need the count, return it without fetching details
    if (count_only && !categorize) {
      return {
        success: true,
        count: res.data.messages.length,
        message: `You have ${res.data.messages.length} unread email(s).`
      };
    }

    const emails = [];
    for (const msg of res.data.messages) {
      const email = await gmail.users.messages.get({ userId: "me", id: msg.id });
      const from = getHeader(email.data.payload.headers, "From");
      const subject = getHeader(email.data.payload.headers, "Subject");
      const date = getHeader(email.data.payload.headers, "Date");
      const snippet = email.data.snippet || "";

      emails.push({
        id: msg.id,
        from,
        subject,
        date,
        snippet,
        unread: email.data.labelIds.includes("UNREAD")
      });
    }

    // If categorization is requested, analyze and categorize the emails
    if (categorize) {
      const categorized = categorizeEmails(emails);
      return {
        success: true,
        emails: count_only ? [] : emails, // Only include full email details if not count_only
        count: emails.length,
        categories: categorized,
        message: `Found ${emails.length} unread email(s). ${categorized.critical.length} critical, ${categorized.important.length} important, ${categorized.normal.length} normal, ${categorized.low.length} low priority.`
      };
    }

    return {
      success: true,
      emails,
      count: emails.length,
      message: `Found ${emails.length} unread email(s).`
    };
  } catch (error) {
    console.error("Error fetching emails:", error);
    return {
      success: false,
      emails: [],
      count: 0,
      message: `Error fetching emails: ${error.message}`
    };
  }
}

/**
 * Categorize emails by importance
 * @param {Array} emails - Array of email objects
 * @returns {Object} - Object containing categorized emails
 */
function categorizeEmails(emails) {
  // Initialize categories
  const categories = {
    critical: [],
    important: [],
    normal: [],
    low: []
  };

  // Keywords that might indicate importance
  const criticalKeywords = ['urgent', 'critical', 'emergency', 'immediate', 'alert', 'security', 'password', 'payment due', 'overdue', 'important'];
  const importantKeywords = ['important', 'attention', 'required', 'action', 'reminder', 'deadline', 'review', 'confirm'];
  const lowPriorityKeywords = ['newsletter', 'promotion', 'offer', 'discount', 'sale', 'marketing', 'subscribe', 'unsubscribe'];

  // Important senders (example domains that might be important)
  const importantSenders = ['bank', 'credit', 'financial', 'tax', 'irs.gov', 'healthcare', 'doctor', 'medical', 'insurance', 'bill', 'invoice'];

  // Categorize each email
  emails.forEach(email => {
    const subject = email.subject.toLowerCase();
    const from = email.from.toLowerCase();

    // Check for critical emails
    if (criticalKeywords.some(keyword => subject.includes(keyword))) {
      categories.critical.push(email);
      return;
    }

    // Check for important senders
    if (importantSenders.some(sender => from.includes(sender))) {
      categories.important.push(email);
      return;
    }

    // Check for important keywords
    if (importantKeywords.some(keyword => subject.includes(keyword))) {
      categories.important.push(email);
      return;
    }

    // Check for low priority
    if (lowPriorityKeywords.some(keyword => subject.includes(keyword) || from.includes(keyword))) {
      categories.low.push(email);
      return;
    }

    // Default to normal priority
    categories.normal.push(email);
  });

  return categories;
}

/**
 * Search emails in Gmail
 * @param {string} query - Gmail search query
 * @param {number} maxResults - Maximum number of emails to fetch
 * @returns {Object} - Object containing success status, emails array, and message
 */
export async function searchEmails(query, maxResults = 10) {
  try {
    const auth = getOAuthClient();
    const gmail = google.gmail({ version: "v1", auth });

    const res = await gmail.users.messages.list({
      userId: "me",
      q: query,
      maxResults: maxResults,
    });

    if (!res.data.messages) {
      return {
        success: true,
        emails: [],
        message: `No emails found matching query: ${query}`
      };
    }

    const emails = [];
    for (const msg of res.data.messages) {
      const email = await gmail.users.messages.get({ userId: "me", id: msg.id });
      const from = getHeader(email.data.payload.headers, "From");
      const to = getHeader(email.data.payload.headers, "To");
      const subject = getHeader(email.data.payload.headers, "Subject");
      const date = getHeader(email.data.payload.headers, "Date");
      const snippet = email.data.snippet || "";

      emails.push({
        id: msg.id,
        from,
        to,
        subject,
        date,
        snippet,
        unread: email.data.labelIds.includes("UNREAD")
      });
    }

    return {
      success: true,
      emails,
      message: `Found ${emails.length} email(s) matching query: ${query}`
    };
  } catch (error) {
    console.error("Error searching emails:", error);
    return {
      success: false,
      emails: [],
      message: `Error searching emails: ${error.message}`
    };
  }
}

/**
 * Get email details including the body content
 * @param {string} messageId - The ID of the email message
 * @returns {Object} - Object containing email details
 */
export async function getEmailDetails(messageId) {
  try {
    const auth = getOAuthClient();
    const gmail = google.gmail({ version: "v1", auth });

    const email = await gmail.users.messages.get({
      userId: "me",
      id: messageId,
      format: "full"
    });

    // Extract headers
    const from = getHeader(email.data.payload.headers, "From");
    const to = getHeader(email.data.payload.headers, "To");
    const subject = getHeader(email.data.payload.headers, "Subject");
    const date = getHeader(email.data.payload.headers, "Date");

    // Extract body content
    let body = "";

    // Function to extract body from parts recursively
    function getBodyFromParts(part) {
      if (part.mimeType === "text/plain" && part.body.data) {
        return Buffer.from(part.body.data, 'base64').toString('utf8');
      } else if (part.mimeType === "text/html" && part.body.data) {
        // For HTML, we could convert to plain text or return as is
        return Buffer.from(part.body.data, 'base64').toString('utf8');
      } else if (part.parts) {
        // Recursively check parts
        for (const subPart of part.parts) {
          const subBody = getBodyFromParts(subPart);
          if (subBody) {
            return subBody;
          }
        }
      }
      return "";
    }

    // Check if the email has a simple body or parts
    if (email.data.payload.body && email.data.payload.body.data) {
      body = Buffer.from(email.data.payload.body.data, 'base64').toString('utf8');
    } else if (email.data.payload.parts) {
      body = getBodyFromParts(email.data.payload);
    }

    return {
      success: true,
      email: {
        id: messageId,
        from,
        to,
        subject,
        date,
        body,
        snippet: email.data.snippet || "",
        unread: email.data.labelIds.includes("UNREAD")
      }
    };
  } catch (error) {
    console.error("Error getting email details:", error);
    return {
      success: false,
      email: null,
      message: `Error getting email details: ${error.message}`
    };
  }
}

/**
 * Mark an email as read
 * @param {string} messageId - The ID of the email message
 * @returns {Object} - Object containing success status and message
 */
export async function markAsRead(messageId) {
  try {
    // Validate messageId
    if (!messageId) {
      return {
        success: false,
        message: "Error: Email ID is required"
      };
    }
    
    console.log(`Attempting to mark email as read: ${messageId}`);
    
    const auth = getOAuthClient();
    const gmail = google.gmail({ version: "v1", auth });

    await gmail.users.messages.modify({
      userId: "me",
      id: messageId,
      requestBody: {
        removeLabelIds: ["UNREAD"]
      }
    });

    return {
      success: true,
      message: "Email marked as read."
    };
  } catch (error) {
    console.error("Error marking email as read:", error);
    return {
      success: false,
      message: `Error marking email as read: ${error.message}`
    };
  }
}

/**
 * Get email labels/folders
 * @returns {Object} - Object containing success status and labels array
 */
export async function getLabels() {
  try {
    const auth = getOAuthClient();
    const gmail = google.gmail({ version: "v1", auth });

    const response = await gmail.users.labels.list({ userId: "me" });

    return {
      success: true,
      labels: response.data.labels,
      message: `Found ${response.data.labels.length} labels.`
    };
  } catch (error) {
    console.error("Error getting labels:", error);
    return {
      success: false,
      labels: [],
      message: `Error getting labels: ${error.message}`
    };
  }
}

// Helper function to get header value
function getHeader(headers, name) {
  const header = headers.find((h) => h.name.toLowerCase() === name.toLowerCase());
  return header ? header.value : "N/A";
}

/**
 * Find and mark an email as read by subject and sender
 * @param {string} subject - The subject to search for (can be partial)
 * @param {string} sender - The sender to search for (can be partial)
 * @returns {Object} - Object containing success status and message
 */
export async function findAndMarkAsRead(subject, sender) {
  try {
    console.log(`Finding and marking email as read - Subject: "${subject}", Sender: "${sender}"`);
    
    if (!subject && !sender) {
      return {
        success: false,
        message: "Error: Subject or sender is required to find the email"
      };
    }
    
    // Build search query
    let query = "is:unread ";
    if (subject) {
      query += `subject:"${subject}" `;
    }
    if (sender) {
      // Handle both full sender name and email address
      if (sender.includes('@')) {
        query += `from:${sender} `;
      } else {
        query += `from:(${sender}) `;
      }
    }
    
    console.log(`Search query: ${query}`);
    
    const auth = getOAuthClient();
    const gmail = google.gmail({ version: "v1", auth });
    
    // Search for matching emails
    const res = await gmail.users.messages.list({
      userId: "me",
      q: query,
      maxResults: 5,
    });
    
    if (!res.data.messages || res.data.messages.length === 0) {
      return {
        success: false,
        message: `No matching unread emails found for subject "${subject}" from "${sender}"`
      };
    }
    
    // Get the first matching email
    const messageId = res.data.messages[0].id;
    console.log(`Found matching email with ID: ${messageId}`);
    
    // Mark it as read
    const markResult = await markAsRead(messageId);
    if (!markResult.success) {
      return {
        success: false,
        message: `Found email but failed to mark as read: ${markResult.message}`
      };
    }
    
    // Get email details for confirmation
    const email = await gmail.users.messages.get({ userId: "me", id: messageId });
    const emailSubject = getHeader(email.data.payload.headers, "Subject") || "N/A";
    const emailFrom = getHeader(email.data.payload.headers, "From") || "N/A";
    
    return {
      success: true,
      message: `Successfully marked email as read - Subject: "${emailSubject}", From: "${emailFrom}"`,
      emailId: messageId
    };
  } catch (error) {
    console.error("Error finding and marking email as read:", error);
    return {
      success: false,
      message: `Error finding and marking email as read: ${error.message}`
    };
  }
}
