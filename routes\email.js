/**
 * Email Routes
 * 
 * Provides API endpoints for accessing Gmail emails.
 */

import express from 'express';
import { 
  getUnreadEmails, 
  searchEmails, 
  getEmailDetails, 
  markAsRead,
  getLabels,
  findAndMarkAsRead
} from '../email.js';

const router = express.Router();

/**
 * GET /api/email/unread
 * Get unread emails
 */
router.get('/unread', async (req, res) => {
  try {
    const maxResults = req.query.limit ? parseInt(req.query.limit) : 5;
    const result = await getUnreadEmails(maxResults);
    
    return res.json(result);
  } catch (error) {
    console.error('Error fetching unread emails:', error);
    return res.status(500).json({ 
      success: false, 
      message: `Error fetching unread emails: ${error.message}` 
    });
  }
});

/**
 * GET /api/email/search
 * Search emails with a query
 */
router.get('/search', async (req, res) => {
  try {
    const { query } = req.query;
    
    if (!query) {
      return res.status(400).json({ 
        success: false, 
        message: 'Search query is required' 
      });
    }
    
    const maxResults = req.query.limit ? parseInt(req.query.limit) : 10;
    const result = await searchEmails(query, maxResults);
    
    return res.json(result);
  } catch (error) {
    console.error('Error searching emails:', error);
    return res.status(500).json({ 
      success: false, 
      message: `Error searching emails: ${error.message}` 
    });
  }
});

/**
 * GET /api/email/:id
 * Get email details by ID
 */
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    
    if (!id) {
      return res.status(400).json({ 
        success: false, 
        message: 'Email ID is required' 
      });
    }
    
    const result = await getEmailDetails(id);
    
    return res.json(result);
  } catch (error) {
    console.error('Error getting email details:', error);
    return res.status(500).json({ 
      success: false, 
      message: `Error getting email details: ${error.message}` 
    });
  }
});

/**
 * PUT /api/email/:id/read
 * Mark an email as read
 */
router.put('/:id/read', async (req, res) => {
  try {
    const { id } = req.params;
    
    if (!id) {
      return res.status(400).json({ 
        success: false, 
        message: 'Email ID is required' 
      });
    }
    
    const result = await markAsRead(id);
    
    return res.json(result);
  } catch (error) {
    console.error('Error marking email as read:', error);
    return res.status(500).json({ 
      success: false, 
      message: `Error marking email as read: ${error.message}` 
    });
  }
});

/**
 * GET /api/email/labels
 * Get email labels/folders
 */
router.get('/labels', async (_req, res) => {
  try {
    const result = await getLabels();
    
    return res.json(result);
  } catch (error) {
    console.error('Error getting email labels:', error);
    return res.status(500).json({ 
      success: false, 
      message: `Error getting email labels: ${error.message}` 
    });
  }
});

/**
 * POST /api/email/find-and-mark-read
 * Find and mark an email as read by subject and/or sender
 */
router.post('/find-and-mark-read', async (req, res) => {
  try {
    const { subject, sender } = req.body;
    
    if (!subject && !sender) {
      return res.status(400).json({ 
        success: false, 
        message: 'Either subject or sender is required' 
      });
    }
    
    const result = await findAndMarkAsRead(subject, sender);
    
    return res.json(result);
  } catch (error) {
    console.error('Error finding and marking email as read:', error);
    return res.status(500).json({ 
      success: false, 
      message: `Error finding and marking email as read: ${error.message}` 
    });
  }
});

export default router;

