/* Email View Styling */
.email-container {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin: 10px 0;
  max-width: 100%;
}

.email-card {
  background-color: #1e1e1e;
  border-radius: 8px;
  padding: 16px;
  border-left: 4px solid #3b82f6;
  transition: all 0.2s ease;
  margin-bottom: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  position: relative;
  overflow: hidden;
}

.email-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to right, rgba(255,255,255,0.03), transparent);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.email-card:hover::before {
  opacity: 1;
}

.email-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.email-card.unread {
  border-left-color: #10b981; /* Green for unread */
}

/* Priority indicators with better visual hierarchy */
.email-card.priority-high {
  border-left-color: #ef4444;
  background-color: rgba(239, 68, 68, 0.1);
}

.email-card.priority-medium {
  border-left-color: #f59e0b;
  background-color: rgba(245, 158, 11, 0.1);
}

.email-card.priority-low {
  border-left-color: #3b82f6;
  background-color: rgba(59, 130, 246, 0.05);
}

.priority-indicator {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  margin-right: 8px;
}

.priority-indicator.high {
  color: #ef4444;
}

.priority-indicator.medium {
  color: #f59e0b;
}

.priority-indicator.low {
  color: #3b82f6;
}

/* Category badges */
.category-badge {
  font-size: 0.7rem;
  padding: 2px 6px;
  border-radius: 4px;
  margin-left: 8px;
  text-transform: uppercase;
  font-weight: bold;
}

.category-badge.finance {
  background-color: #10b981;
  color: #f0fdf4;
}

.category-badge.travel {
  background-color: #6366f1;
  color: #eef2ff;
}

.category-badge.shopping {
  background-color: #f97316;
  color: #fff7ed;
}

.category-badge.social {
  background-color: #ec4899;
  color: #fdf2f8;
}

.category-badge.work {
  background-color: #8b5cf6;
  color: #f5f3ff;
}

/* AI analysis section */
.ai-analysis {
  font-size: 0.75rem;
  color: #9ca3af;
  margin-top: 8px;
  padding: 4px 8px;
  background-color: rgba(30, 41, 59, 0.5);
  border-radius: 4px;
  display: inline-block;
}

.ai-analysis i {
  margin-right: 4px;
  color: #60a5fa;
}

.email-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
}

.email-sender {
  font-weight: bold;
  color: #e5e7eb;
}

.email-date {
  font-size: 0.8em;
  color: #9ca3af;
}

.email-subject {
  font-size: 1.1em;
  margin-bottom: 8px;
  color: #f3f4f6;
}

.email-snippet {
  color: #d1d5db;
  font-size: 0.9em;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.email-actions {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 12px;
}

.email-action-button {
  padding: 6px 12px;
  border-radius: 4px;
  background-color: #2d3748;
  color: #e2e8f0;
  border: none;
  font-size: 0.8rem;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 4px;
}

.email-action-button:hover {
  background-color: #4a5568;
}

.email-action-button i {
  font-size: 0.75rem;
}

/* Sender email styling */
.sender-email {
  color: #9ca3af;
  font-weight: normal;
  font-size: 0.9em;
}

.email-expanded {
  margin-top: 8px;
  padding-top: 8px;
  border-top: 1px solid #4b5563;
  color: #d1d5db;
  font-size: 0.9em;
  line-height: 1.5;
}

/* Animation for expanding/collapsing */
.email-expanded {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease;
}

.email-expanded.show {
  max-height: 500px;
}

/* Badge for email count */
.email-badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  background-color: #3b82f6;
  color: white;
  border-radius: 9999px;
  padding: 2px 8px;
  font-size: 0.8em;
  font-weight: bold;
  margin-left: 8px;
}

/* Email controls */
.email-controls {
  background-color: #262626;
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 16px;
}

.view-mode-selector {
  margin-bottom: 8px;
}

/* Cluster styling */
.cluster-container {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.cluster-card {
  background-color: #262626;
  border-radius: 8px;
  padding: 12px;
  transition: all 0.2s ease;
}

.cluster-card:hover {
  background-color: #2d2d2d;
}

.cluster-summary {
  color: #9ca3af;
  font-size: 0.9em;
  line-height: 1.4;
}

.cluster-expand-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.cluster-expand-btn:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

/* Email sender container */
.email-sender-container {
  display: flex;
  align-items: center;
}

/* Email priority reasons */
.email-priority-reasons {
  margin-top: 4px;
  margin-bottom: 8px;
  font-size: 0.8em;
}


