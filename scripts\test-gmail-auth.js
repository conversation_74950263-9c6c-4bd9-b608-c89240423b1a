/**
 * Test script for Gmail OAuth authentication
 *
 * This script handles the initial authentication process for Gmail access.
 * It will generate an authorization URL, prompt the user to visit it,
 * and then save the resulting token for future use.
 *
 * Run with: node scripts/test-gmail-auth.js
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { google } from 'googleapis';
import readline from 'readline';
import dotenv from 'dotenv';
import { SCOPES } from '../email.js';

// Load environment variables
dotenv.config();

// Get the directory name
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Path to token file
const TOKEN_PATH = path.join(__dirname, '..', 'config', 'token.json');

// Make sure the config directory exists
fs.mkdirSync(path.dirname(TOKEN_PATH), { recursive: true });

// Create readline interface for user input
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

/**
 * Get and store new token after prompting for user authorization
 */
async function getNewToken() {
  // Use hardcoded credentials
  const client_id = '122591791863-e7o6l17e9k3i4e6kogr96t99hdffvkma.apps.googleusercontent.com';
  const client_secret = '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';
  const redirect_uri = 'http://localhost:3081/oauth2callback';

  console.log('Using hardcoded OAuth credentials for Gmail API access');

  // Create OAuth client
  const oAuth2Client = new google.auth.OAuth2(client_id, client_secret, redirect_uri);

  // Generate authorization URL
  const authUrl = oAuth2Client.generateAuthUrl({
    access_type: 'offline',
    scope: SCOPES,
  });

  console.log('Authorize this app by visiting this URL:');
  console.log(authUrl);

  // Prompt user for the authorization code
  rl.question('Enter the code from that page here: ', async (code) => {
    try {
      // Exchange authorization code for access token
      const { tokens } = await oAuth2Client.getToken(code);
      oAuth2Client.setCredentials(tokens);

      // Store the token to disk for later program executions
      fs.mkdirSync(path.dirname(TOKEN_PATH), { recursive: true });
      fs.writeFileSync(TOKEN_PATH, JSON.stringify(tokens));
      console.log('Token stored to', TOKEN_PATH);

      // Test the token by fetching unread emails
      testToken(oAuth2Client);
    } catch (error) {
      console.error('Error retrieving access token:', error);
      rl.close();
      process.exit(1);
    }
  });
}

/**
 * Test the token by fetching unread emails
 */
async function testToken(auth) {
  try {
    const gmail = google.gmail({ version: 'v1', auth });

    console.log('Testing token by fetching unread emails...');

    const res = await gmail.users.messages.list({
      userId: 'me',
      labelIds: ['INBOX'],
      q: 'is:unread',
      maxResults: 3,
    });

    if (!res.data.messages) {
      console.log('No unread emails found.');
    } else {
      console.log(`Found ${res.data.messages.length} unread email(s).`);

      // Fetch details for each message
      for (const msg of res.data.messages) {
        const email = await gmail.users.messages.get({ userId: 'me', id: msg.id });
        const from = email.data.payload.headers.find(h => h.name === 'From')?.value || 'N/A';
        const subject = email.data.payload.headers.find(h => h.name === 'Subject')?.value || 'N/A';

        console.log(`- From: ${from}`);
        console.log(`  Subject: ${subject}`);
        console.log();
      }
    }

    console.log('Authentication successful! You can now use the email functionality.');
    rl.close();
  } catch (error) {
    console.error('Error testing token:', error);
    rl.close();
    process.exit(1);
  }
}

// Start the authentication process
getNewToken();
