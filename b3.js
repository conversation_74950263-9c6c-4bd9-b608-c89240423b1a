import { GoogleGenerativeAI } from "@google/generative-ai";
import dotenv from 'dotenv';

dotenv.config();

// Initialize Gemini
const genAI = new GoogleGenerativeAI(process.env.GOOGLE_API_KEY);
const preparationModel = genAI.getGenerativeModel({ 
    model: "gemini-1.5-flash",
    systemInstruction: `<PERSON> are <PERSON><PERSON>, a curious ai assistant design for male companionship.`
 }); // Choose appropriate model

// Prompt Template for the Preparation Agent
const preparationPrompt = `You are an AI agent designed to prepare email data for an importance scoring LLM.
Your task is to take the raw email data, the output of a Natural Language Understanding (NLU) analysis, user context, and importance scoring instructions and structure them into a concise JSON object that will be given to the scoring LLM.

Here's the raw email data:
\`\`\`json
{{email_data}}\`\`\`

Here's the NLU analysis of the email:
\`\`\`json
{{nlu_analysis}}\`\`\`

Here's the user's context:
\`\`\`json
{{user_context}}\`\`\`

Here are the importance scoring instructions that the scoring LLM will use:
\`\`\`json
{{importance_scoring_instructions}}\`\`\`

Based on all of the information above, create a JSON object with the following structure:

\`\`\`json
{
  "email_summary": "(A concise summary of the email's content)",
  "sender_importance": "(Sender's importance level, if available)",
  "intent": "(The primary intent of the email)",
  "sentiment": "(The sentiment of the email)",
  "urgency_indicators": "(List of urgency indicators found in the email)",
  "has_attachment": "(Boolean: true if the email has an attachment, false otherwise)",
  "user_preferences": "(Relevant user preferences that might affect importance)",
  "scoring_instructions": "(Copy of the importance scoring instructions)",
  "reasoning_prompt": "(A short prompt to guide the scoring LLM's reasoning)"
}
\`\`\`

Ensure the output is a valid JSON object.`;

/**
 * Prepares email data for scoring by an LLM using @google/generative-ai.
 * @param {object} email - The raw email data object.
 * @param {object} nluAnalysis - The NLU analysis result from the first LLM.
 * @param {object} userContext - The user's context information.
 * @param {object} importanceScoringInstructions - The instructions for scoring email importance.
 * @returns {object|null} - A JSON object ready for the scoring LLM, or null on error.
 */
async function prepareEmailForScoring(email, nluAnalysis, userContext, importanceScoringInstructions) {
    try {
        const emailData = {
            from: email.email_metadata.sender,
            subject: email.email_metadata.subject,
            date_sent: email.email_metadata.date_sent,
            text: email.text // Assuming you have email.text
        };

        const prompt = preparationPrompt
            .replace("{{email_data}}", JSON.stringify(emailData, null, 2))
            .replace("{{nlu_analysis}}", JSON.stringify(nluAnalysis, null, 2))
            .replace("{{user_context}}", JSON.stringify(userContext, null, 2))
            .replace("{{importance_scoring_instructions}}", JSON.stringify(importanceScoringInstructions, null, 2));

        const result = await preparationModel.generateContent([prompt]);
        const responseText = result.response?.candidates?.[0]?.content?.parts?.[0]?.text;

        if (responseText) {
            try {
                // Attempt to parse the response as JSON
                let jsonString = responseText.trim();
                if (jsonString.startsWith("```")) {
                    jsonString = jsonString.replace(/^```(?:json)?/, '').replace(/```$/, '').trim();
                }
                const preparedData = JSON.parse(jsonString);
                return preparedData;
            } catch (parseError) {
                console.error("Error parsing LLM response:", parseError, responseText);
                return null;
            }
        } else {
            console.warn("Preparation LLM returned empty response.");
            return null;
        }

    } catch (error) {
        console.error("Error preparing email for scoring:", error);
        return null;
    }
}

// Example Usage (Integrate this into your main Node.js flow)
async function main() {
    // Sample Data (Replace with your actual data)
    const sampleEmail = {
        id: "123",
        email_metadata: {
            sender: { name: "John Doe", email_address: "<EMAIL>", importance_level: "high" },
            subject: "Urgent Project Alpha Deadline",
            date_sent: "2025-04-11T10:00:00Z",
            has_attachment: true
        },
        text: "The Project Alpha deadline is this Friday! Please submit your reports ASAP.",
        nlp_analysis: {} // This will be populated by your NLU LLM
    };

    const sampleNluAnalysis = {
        sentiment: { label: "negative", confidence: 0.8 },
        intent: { label: "action_needed", confidence: 0.9 },
        entities: { date: ["this Friday"], project: ["Project Alpha"] },
        urgency_indicators: ["deadline", "urgent"],
        keywords: ["deadline", "urgent", "report", "Project Alpha"]
    };

    const sampleUserContext = {
        current_time: "2025-04-11T18:49:00-04:00", // Updated time
        current_location: "Winter Haven, Florida, United States",
        user_preferences: {
            prioritize_from_vip: true,
            keywords_high_priority: ["deadline", "Project Alpha"]
        }
    };

    const sampleImportanceScoringInstructions = {
        high_priority_indicators: [
            "sender.importance_level == 'high'",
            "nlp_analysis.intent.label == 'action_needed'",
            "'deadline' in nlp_analysis.keywords",
            "'Project Alpha' in nlp_analysis.entities.project"
        ],
        weighting_factors: {
            sender_importance: 0.4,
            intent_urgency: 0.3,
            deadline_present: 0.3
        },
        scoring_logic_description: "Score based on sender, intent, and deadline."
    };

    sampleEmail.nlp_analysis = sampleNluAnalysis;

    const preparedData = await prepareEmailForScoring(
        sampleEmail,
        sampleNluAnalysis,
        sampleUserContext,
        sampleImportanceScoringInstructions
    );

    if (preparedData) {
        console.log("Prepared Data for Scoring LLM:");
        console.log(JSON.stringify(preparedData, null, 2));
    }
}

main(); // Uncomment to run the example