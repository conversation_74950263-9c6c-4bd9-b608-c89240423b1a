        body {
            background-color: #0f0f0f;
            color: white;
        }

        /* Markdown styling */
        .markdown-content {
            line-height: 1.6;
        }

        .markdown-content h1 {
            font-size: 1.8em;
            margin-top: 1em;
            margin-bottom: 0.5em;
            font-weight: bold;
        }

        .markdown-content h2 {
            font-size: 1.5em;
            margin-top: 1em;
            margin-bottom: 0.5em;
            font-weight: bold;
        }

        .markdown-content h3 {
            font-size: 1.3em;
            margin-top: 1em;
            margin-bottom: 0.5em;
            font-weight: bold;
        }

        .markdown-content p {
            margin-bottom: 1em;
        }

        .markdown-content ul, .markdown-content ol {
            margin-left: 2em;
            margin-bottom: 1em;
        }

        .markdown-content ul {
            list-style-type: disc;
        }

        .markdown-content ol {
            list-style-type: decimal;
        }

        .markdown-content li {
            margin-bottom: 0.5em;
        }

        .markdown-content code {
            font-family: monospace;
            background-color: #1e1e1e;
            padding: 0.2em 0.4em;
            border-radius: 3px;
            font-size: 0.9em;
        }

        .markdown-content pre {
            background-color: #1e1e1e;
            padding: 1em;
            border-radius: 5px;
            overflow-x: auto;
            margin-bottom: 1em;
        }

        .markdown-content pre code {
            background-color: transparent;
            padding: 0;
            border-radius: 0;
        }

        .markdown-content blockquote {
            border-left: 4px solid #3b82f6;
            padding-left: 1em;
            margin-left: 0;
            margin-bottom: 1em;
            color: #d1d5db;
        }

        .markdown-content a {
            color: #3b82f6;
            text-decoration: underline;
        }

        .markdown-content table {
            border-collapse: collapse;
            margin-bottom: 1em;
            width: 100%;
        }

        .markdown-content th, .markdown-content td {
            border: 1px solid #4b5563;
            padding: 0.5em;
            text-align: left;
        }

        .markdown-content th {
            background-color: #1f2937;
        }

        body {
            font-family: 'Google Sans', 'Roboto', sans-serif;
        }
        .recording-pulse {
            animation: pulse 1.5s infinite;
            color: #f87171 !important; /* Red color when recording */
        }
        @keyframes pulse {
            0% {
                transform: scale(1);
                opacity: 1;
            }
            50% {
                transform: scale(1.1);
                opacity: 0.7;
            }
            100% {
                transform: scale(1);
                opacity: 1;
            }
        }

        .recording-indicator {
            position: absolute;
            bottom: -20px;
            left: 50%;
            transform: translateX(-50%);
            color: #f87171;
            font-size: 12px;
            white-space: nowrap;
            animation: fadeInOut 1.5s infinite;
        }

        @keyframes fadeInOut {
            0% { opacity: 0.7; }
            50% { opacity: 1; }
            100% { opacity: 0.7; }
        }

        /* System message styling */
        .system-message {
            font-size: 0.9rem;
            opacity: 0.9;
            max-width: 80%;
            margin: 0 auto;
            transition: opacity 0.3s ease;
        }

        .system-message:hover {
            opacity: 1;
        }
        .gradient-text {
            background: linear-gradient(90deg, #8ab4f8 0%, #f28b82 100%);
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
        }
        .sidebar {
            transition: width 0.3s ease;
        }
        .sidebar.collapsed {
            width: 4rem;
        }
        .sidebar.expanded {
            width: 16rem;
        }

        /* Truncate text in sidebar items */
        .sidebar-item-text {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 180px;
        }

        /* Styling for active sidebar item */
        .sidebar-item-active {
            background-color: rgba(59, 130, 246, 0.3);
        }

        /* Dot indicator */
        .dot-indicator {
            width: 6px;
            height: 6px;
            border-radius: 50%;
            background-color: #f87171;
            margin-left: auto;
        }
        .sidebar-icon {
            transition: all 0.2s ease;
        }
        .sidebar-icon:hover {
            background-color: #292929;
        }
        .code-block {
            background-color: #2d2d2d;
            border-radius: 4px;
            padding: 2px 6px;
            font-family: monospace;
            display: inline-block;
        }
        .blue-star {
            color: #4285f4;
            font-size: 24px;
        }

        .user-bubble {
            background-color: #303030;
            border-radius: 24px;
            padding: 16px;
        }

        .input-container {
            border-radius: 24px;
            background-color: #303030;
        }

        /* Show/hide elements based on sidebar state */
        .sidebar-expanded-content {
            display: none;
        }

        .sidebar.expanded .sidebar-expanded-content {
            display: block;
        }

        .sidebar.expanded .sidebar-collapsed-content {
            display: none;
        }

        /* Image upload styles */
        .image-preview {
            max-width: 100%;
            max-height: 300px;
            border-radius: 8px;
            margin-top: 10px;
        }

        /* Image preview container */
        .uploaded-images-container {
            padding: 10px 0;
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-bottom: 10px;
            background-color: #1a1a1a;
            border-radius: 12px;
            padding: 12px;
        }

        .image-preview-item {
            position: relative;
            width: 120px;
            height: 120px;
            transition: all 0.2s ease;
        }

        .image-preview-item:hover {
            transform: scale(1.05);
        }

        .image-preview-item img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 8px;
            border: 1px solid #3a3a3a;
        }

        .remove-image-btn {
            position: absolute;
            top: -5px;
            right: -5px;
            background-color: #303030;
            border-radius: 50%;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            border: 1px solid #444;
            color: #ccc;
            transition: all 0.2s ease;
        }

        .remove-image-btn:hover {
            background-color: #505050;
            color: white;
        }

        /* Loading animation */
        @keyframes pulse-opacity {
            0% { opacity: 0.4; }
            50% { opacity: 1; }
            100% { opacity: 0.4; }
        }

        .animate-pulse {
            animation: pulse-opacity 1.5s infinite ease-in-out;
        }

        /* Markdown styling */
        .markdown-content h1 {
            font-size: 1.5rem;
            font-weight: bold;
            margin-top: 1.5rem;
            margin-bottom: 0.75rem;
            color: #e2e8f0;
        }

        /* Fallback code highlighting */
        .fallback-highlight {
            display: block;
            padding: 1em;
            background-color: #282c34;
            color: #abb2bf;
            border-radius: 0.3em;
            overflow-x: auto;
            font-family: monospace;
        }

        /* Improved code block styling */
        pre {
            margin: 1.5em 0;
            border-radius: 8px;
            background-color: #1e1e1e !important;
            overflow-x: auto;
            position: relative;
            padding-top: 2.5em; /* Space for language label */
            max-width: 100%;
            width: 100%;
        }

        /* Language label */
        .code-language-label {
            position: absolute;
            top: 0;
            right: 0;
            background-color: #333;
            color: #fff;
            padding: 0.3em 0.8em;
            font-size: 0.8em;
            border-radius: 0 8px 0 8px;
            font-family: 'Consolas', 'Monaco', monospace;
            z-index: 1;
        }

        pre code {
            display: block;
            padding: 1.5em !important;
            font-family: 'Consolas', 'Monaco', 'Andale Mono', 'Ubuntu Mono', monospace !important;
            font-size: 16px !important;
            line-height: 1.6 !important;
            color: #e6e6e6 !important;
            tab-size: 4;
            hyphens: none;
            white-space: pre-wrap; /* Changed from pre to pre-wrap to allow wrapping */
            word-spacing: normal;
            word-break: break-word; /* Changed from normal to break-word */
            overflow-wrap: break-word; /* Added to prevent overflow */
            max-width: 100%; /* Ensure code doesn't exceed container width */
        }

        /* Ensure markdown content fits within container */
        .markdown-content {
            width: 100%;
            max-width: 100%;
            overflow-wrap: break-word;
        }

        /* Ensure code blocks fit within markdown content */
        .markdown-content pre {
            width: 100%;
            max-width: 100%;
        }

        /* Syntax highlighting colors */
        .hljs-keyword {
            color: #569CD6 !important; /* blue */
        }

        .hljs-string {
            color: #CE9178 !important; /* orange-red */
        }

        .hljs-comment {
            color: #6A9955 !important; /* green */
        }

        .hljs-tag {
            color: #569CD6 !important; /* blue */
        }

        .hljs-attr {
            color: #9CDCFE !important; /* light blue */
        }

        .hljs-name {
            color: #569CD6 !important; /* blue */
        }

        .hljs-built_in {
            color: #4EC9B0 !important; /* teal */
        }

        .markdown-content h2 {
            font-size: 1.25rem;
            font-weight: bold;
            margin-top: 1.25rem;
            margin-bottom: 0.5rem;
            color: #e2e8f0;
        }

        .markdown-content h3 {
            font-size: 1.125rem;
            font-weight: bold;
            margin-top: 1rem;
            margin-bottom: 0.5rem;
            color: #e2e8f0;
        }

        .markdown-content p {
            margin-bottom: 0.75rem;
            line-height: 1.6;
        }

        .markdown-content ul, .markdown-content ol {
            margin-left: 1.5rem;
            margin-bottom: 0.75rem;
        }

        .markdown-content ul {
            list-style-type: disc;
        }

        .markdown-content ol {
            list-style-type: decimal;
        }

        .markdown-content li {
            margin-bottom: 0.25rem;
        }

        .markdown-content a {
            color: #3b82f6;
            text-decoration: underline;
        }

        .markdown-content blockquote {
            border-left: 3px solid #4b5563;
            padding-left: 1rem;
            margin-left: 0;
            margin-right: 0;
            font-style: italic;
            color: #9ca3af;
        }

        .markdown-content code:not(pre code) {
            font-family: monospace;
            background-color: #1e293b;
            padding: 0.125rem 0.25rem;
            border-radius: 0.25rem;
            font-size: 0.875em;
            color: #e2e8f0;
        }

        .markdown-content pre {
            background-color: #1e293b;
            padding: 1rem;
            border-radius: 0.5rem;
            overflow-x: auto;
            margin-bottom: 1rem;
        }

        .markdown-content pre code {
            font-family: monospace;
            color: #e2e8f0;
            font-size: 0.875em;
            line-height: 1.5;
        }

        .markdown-content table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 1rem;
        }

        .markdown-content th, .markdown-content td {
            border: 1px solid #4b5563;
            padding: 0.5rem;
            text-align: left;
        }

        .markdown-content th {
            background-color: #1e293b;
            font-weight: bold;
        }

        .markdown-content hr {
            border: 0;
            border-top: 1px solid #4b5563;
            margin: 1.5rem 0;
        }

        /* Mobile Styles */
        @media (max-width: 768px) {
            .sidebar {
                display: none;
                position: fixed;
                top: 0;
                left: 0;
                height: 100%;
                z-index: 50;
                width: 80% !important; /* Override collapsed/expanded width */
                max-width: 300px;
                box-shadow: 0 0 10px rgba(0, 0, 0, 0.5);
            }

            .sidebar-overlay {
                display: none;
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background-color: rgba(0, 0, 0, 0.5);
                z-index: 40;
            }

            .desktop-header {
                display: none;
            }

            .mobile-header {
                display: flex;
            }

            .user-message {
                max-width: 80%;
                margin-left: auto;
                margin-right: 12px;
            }

            .assistant-message {
                padding-right: 40px;
            }

            .options-button {
                display: block;
            }

            /* Always show expanded content on mobile */
            .sidebar .sidebar-expanded-content {
                display: block;
            }

            .sidebar .sidebar-collapsed-content {
                display: none;
            }
        }

        /* Desktop Styles */
        @media (min-width: 769px) {
            .mobile-header {
                display: none;
            }

            .desktop-header {
                display: flex;
            }

            .options-button {
                display: none;
            }
        }
