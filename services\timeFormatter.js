/**
 * Time Formatter Service
 * 
 * Provides utilities for formatting timestamps in various ways:
 * - Absolute time (e.g., "3:45 PM")
 * - Relative time (e.g., "5 minutes ago", "yesterday")
 * - Time period grouping (morning, afternoon, evening, night)
 */

/**
 * Format a timestamp as an absolute time
 * @param {Date} timestamp - The timestamp to format
 * @param {boolean} includeSeconds - Whether to include seconds in the output
 * @returns {string} - Formatted time string (e.g., "3:45 PM")
 */
export function formatAbsoluteTime(timestamp, includeSeconds = false) {
  const date = new Date(timestamp);
  const options = {
    hour: 'numeric',
    minute: 'numeric',
    hour12: true
  };
  
  if (includeSeconds) {
    options.second = 'numeric';
  }
  
  return new Intl.DateTimeFormat('en-US', options).format(date);
}

/**
 * Format a timestamp as an absolute date and time
 * @param {Date} timestamp - The timestamp to format
 * @returns {string} - Formatted date and time string (e.g., "Jan 15, 3:45 PM")
 */
export function formatAbsoluteDateTime(timestamp) {
  const date = new Date(timestamp);
  const options = {
    month: 'short',
    day: 'numeric',
    hour: 'numeric',
    minute: 'numeric',
    hour12: true
  };
  
  return new Intl.DateTimeFormat('en-US', options).format(date);
}

/**
 * Format a timestamp as a relative time
 * @param {Date} timestamp - The timestamp to format
 * @returns {string} - Relative time string (e.g., "just now", "5 minutes ago", "yesterday")
 */
export function formatRelativeTime(timestamp) {
  const now = new Date();
  const date = new Date(timestamp);
  const diffMs = now - date;
  const diffSec = Math.floor(diffMs / 1000);
  const diffMin = Math.floor(diffSec / 60);
  const diffHour = Math.floor(diffMin / 60);
  const diffDay = Math.floor(diffHour / 24);
  
  // Just now (less than 1 minute ago)
  if (diffMin < 1) {
    return 'just now';
  }
  
  // Minutes ago (less than 1 hour ago)
  if (diffHour < 1) {
    return `${diffMin} ${diffMin === 1 ? 'minute' : 'minutes'} ago`;
  }
  
  // Hours ago (less than 24 hours ago)
  if (diffDay < 1) {
    return `${diffHour} ${diffHour === 1 ? 'hour' : 'hours'} ago`;
  }
  
  // Yesterday
  if (diffDay === 1) {
    return 'yesterday';
  }
  
  // Days ago (less than 7 days ago)
  if (diffDay < 7) {
    return `${diffDay} days ago`;
  }
  
  // More than a week ago - return absolute date
  return formatAbsoluteDateTime(timestamp);
}

/**
 * Get the time period for a timestamp
 * @param {Date} timestamp - The timestamp to categorize
 * @returns {string} - Time period (morning, afternoon, evening, night)
 */
export function getTimePeriod(timestamp) {
  const date = new Date(timestamp);
  const hour = date.getHours();
  
  if (hour >= 5 && hour < 12) {
    return 'morning';
  } else if (hour >= 12 && hour < 17) {
    return 'afternoon';
  } else if (hour >= 17 && hour < 21) {
    return 'evening';
  } else {
    return 'night';
  }
}

/**
 * Check if two timestamps are on the same day
 * @param {Date} timestamp1 - First timestamp
 * @param {Date} timestamp2 - Second timestamp
 * @returns {boolean} - Whether the timestamps are on the same day
 */
export function isSameDay(timestamp1, timestamp2) {
  const date1 = new Date(timestamp1);
  const date2 = new Date(timestamp2);
  
  return (
    date1.getFullYear() === date2.getFullYear() &&
    date1.getMonth() === date2.getMonth() &&
    date1.getDate() === date2.getDate()
  );
}

/**
 * Format a date as a day header
 * @param {Date} timestamp - The timestamp to format
 * @returns {string} - Formatted day header (e.g., "Today", "Yesterday", "Monday, January 15")
 */
export function formatDayHeader(timestamp) {
  const now = new Date();
  const date = new Date(timestamp);
  
  // Check if it's today
  if (isSameDay(date, now)) {
    return 'Today';
  }
  
  // Check if it's yesterday
  const yesterday = new Date(now);
  yesterday.setDate(yesterday.getDate() - 1);
  if (isSameDay(date, yesterday)) {
    return 'Yesterday';
  }
  
  // Check if it's within the last week
  const oneWeekAgo = new Date(now);
  oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);
  if (date >= oneWeekAgo) {
    // Return day of week
    return new Intl.DateTimeFormat('en-US', { weekday: 'long' }).format(date);
  }
  
  // Otherwise, return full date
  return new Intl.DateTimeFormat('en-US', { 
    weekday: 'long',
    month: 'long', 
    day: 'numeric' 
  }).format(date);
}

/**
 * Group messages by day
 * @param {Array} messages - Array of message objects with timestamps
 * @returns {Array} - Array of day groups, each containing a header and messages
 */
export function groupMessagesByDay(messages) {
  if (!messages || messages.length === 0) {
    return [];
  }
  
  const groups = [];
  let currentDay = null;
  let currentMessages = [];
  
  messages.forEach(message => {
    const messageDate = new Date(message.timestamp);
    const messageDay = new Date(
      messageDate.getFullYear(),
      messageDate.getMonth(),
      messageDate.getDate()
    ).getTime();
    
    if (currentDay === null) {
      currentDay = messageDay;
    }
    
    if (messageDay !== currentDay) {
      // Save the current group and start a new one
      groups.push({
        date: new Date(currentDay),
        header: formatDayHeader(new Date(currentDay)),
        messages: currentMessages
      });
      
      currentDay = messageDay;
      currentMessages = [message];
    } else {
      currentMessages.push(message);
    }
  });
  
  // Add the last group
  if (currentMessages.length > 0) {
    groups.push({
      date: new Date(currentDay),
      header: formatDayHeader(new Date(currentDay)),
      messages: currentMessages
    });
  }
  
  return groups;
}

/**
 * Group messages by time period within a day
 * @param {Array} messages - Array of message objects with timestamps
 * @returns {Array} - Array of time period groups, each containing a header and messages
 */
export function groupMessagesByTimePeriod(messages) {
  if (!messages || messages.length === 0) {
    return [];
  }
  
  const groups = [];
  let currentPeriod = null;
  let currentMessages = [];
  
  messages.forEach(message => {
    const messagePeriod = getTimePeriod(message.timestamp);
    
    if (currentPeriod === null) {
      currentPeriod = messagePeriod;
    }
    
    if (messagePeriod !== currentPeriod) {
      // Save the current group and start a new one
      groups.push({
        period: currentPeriod,
        header: currentPeriod.charAt(0).toUpperCase() + currentPeriod.slice(1),
        messages: currentMessages
      });
      
      currentPeriod = messagePeriod;
      currentMessages = [message];
    } else {
      currentMessages.push(message);
    }
  });
  
  // Add the last group
  if (currentMessages.length > 0) {
    groups.push({
      period: currentPeriod,
      header: currentPeriod.charAt(0).toUpperCase() + currentPeriod.slice(1),
      messages: currentMessages
    });
  }
  
  return groups;
}

export default {
  formatAbsoluteTime,
  formatAbsoluteDateTime,
  formatRelativeTime,
  getTimePeriod,
  isSameDay,
  formatDayHeader,
  groupMessagesByDay,
  groupMessagesByTimePeriod
};
