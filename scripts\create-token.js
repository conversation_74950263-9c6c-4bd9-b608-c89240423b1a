/**
 * <PERSON><PERSON><PERSON> to create a token.json file with minimal OAuth credentials
 * 
 * This script creates a token.json file that can be used for Gmail API access
 * without requiring the full OAuth flow.
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { google } from 'googleapis';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Get the directory name
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Path to token file
const TOKEN_PATH = path.join(__dirname, '..', 'config', 'token.json');

// Make sure the config directory exists
fs.mkdirSync(path.dirname(TOKEN_PATH), { recursive: true });

// Create a minimal token file
async function createTokenFile() {
  try {
    // Check if token file already exists
    if (fs.existsSync(TOKEN_PATH)) {
      console.log(`Token file already exists at ${TOKEN_PATH}`);
      console.log('Reading existing token...');
      
      const tokenData = JSON.parse(fs.readFileSync(TOKEN_PATH));
      console.log('Token data:', tokenData);
      
      // Create a simple OAuth client
      const oAuth2Client = new google.auth.OAuth2(
        'dummy-client-id.apps.googleusercontent.com',  // Dummy client ID
        'dummy-client-secret',                         // Dummy client secret
        'http://localhost:3081/oauth2callback'         // Dummy redirect URI
      );
      
      // Set the credentials from the token file
      oAuth2Client.setCredentials(tokenData);
      
      // Test the token by fetching unread emails
      console.log('Testing token with Gmail API...');
      const gmail = google.gmail({ version: 'v1', auth: oAuth2Client });
      
      try {
        const res = await gmail.users.messages.list({
          userId: 'me',
          labelIds: ['INBOX'],
          q: 'is:unread',
          maxResults: 3,
        });
        
        if (!res.data.messages) {
          console.log('No unread emails found.');
        } else {
          console.log(`Found ${res.data.messages.length} unread email(s).`);
          
          // Fetch details for each message
          for (const msg of res.data.messages) {
            const email = await gmail.users.messages.get({ userId: 'me', id: msg.id });
            const from = email.data.payload.headers.find(h => h.name === 'From')?.value || 'N/A';
            const subject = email.data.payload.headers.find(h => h.name === 'Subject')?.value || 'N/A';
            
            console.log(`- From: ${from}`);
            console.log(`  Subject: ${subject}`);
            console.log();
          }
        }
        
        console.log('Token is valid and working!');
        
        // Update the email.js file to use dummy client ID and secret
        updateEmailJs();
        
        return true;
      } catch (error) {
        console.error('Error testing token:', error);
        console.log('Token is invalid or expired.');
        return false;
      }
    } else {
      console.log(`Token file does not exist at ${TOKEN_PATH}`);
      console.log('Please create a token.json file with valid OAuth credentials.');
      return false;
    }
  } catch (error) {
    console.error('Error creating token file:', error);
    return false;
  }
}

// Update the email.js file to use dummy client ID and secret
function updateEmailJs() {
  try {
    const emailJsPath = path.join(__dirname, '..', 'email.js');
    let emailJsContent = fs.readFileSync(emailJsPath, 'utf8');
    
    // Replace the getOAuthClient function
    const newFunction = `// Initialize the OAuth client using token.json file
export function getOAuthClient() {
  // Check if token file exists
  if (!fs.existsSync(TOKEN_PATH)) {
    throw new Error("Token file not found. Please run the scripts/test-gmail-auth.js file first to authenticate.");
  }

  // Read token data
  const tokenData = JSON.parse(fs.readFileSync(TOKEN_PATH));
  
  // Create a simple OAuth client with dummy credentials
  // This works because we already have a valid token
  const oAuth2Client = new google.auth.OAuth2(
    'dummy-client-id.apps.googleusercontent.com',  // Dummy client ID
    'dummy-client-secret',                         // Dummy client secret
    'http://localhost:3081/oauth2callback'         // Dummy redirect URI
  );
  
  // Set the credentials from the token file
  oAuth2Client.setCredentials(tokenData);
  
  return oAuth2Client;
}`;
    
    // Find the getOAuthClient function in the file
    const functionRegex = /\/\/ Initialize the OAuth client.*?export function getOAuthClient\(\) \{[\s\S]*?\}/;
    
    // Replace the function
    emailJsContent = emailJsContent.replace(functionRegex, newFunction);
    
    // Write the updated content back to the file
    fs.writeFileSync(emailJsPath, emailJsContent);
    
    console.log('Updated email.js file with dummy client credentials.');
  } catch (error) {
    console.error('Error updating email.js file:', error);
  }
}

// Run the script
createTokenFile();
