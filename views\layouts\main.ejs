<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><%= typeof title !== 'undefined' ? title : 'Virtra UI' %></title>
    <script src="https://cdn.jsdelivr.net/npm/@tailwindcss/browser@4"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <!-- Markdown parser -->
    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
    <!-- Syntax highlighting -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/highlight.js@11.7.0/styles/github-dark.min.css">
    <script src="https://cdn.jsdelivr.net/npm/highlight.js@11.7.0/lib/highlight.min.js"></script>
    <script>
        // Ensure highlight.js is properly initialized
        window.addEventListener('DOMContentLoaded', () => {
            // Create a fallback if highlight.js fails to load
            if (typeof hljs === 'undefined') {
                console.warn('highlight.js not loaded, creating fallback');
                window.hljs = {
                    highlightElement: function(block) {
                        // Simple fallback styling
                        block.classList.add('fallback-highlight');
                    }
                };
            }
        });
    </script>
    <!-- Email view styles -->
    <link rel="stylesheet" href="/css/email-view.css">
    <style>
        body {
            background-color: #0f0f0f;
            color: white;
            font-family: 'Google Sans', 'Roboto', sans-serif;
        }
    </style>
</head>
<body class="flex h-screen overflow-hidden">
    <!-- Main Content -->
    <div class="flex-1 flex flex-col">
        <%- body %>
    </div>
    
    <!-- NLU Debug Panel -->
    <%- include('../partials/nlu-debug') %>
    
    <!-- Scripts -->
    <script src="/js/chat.js"></script>
    <script src="/js/email-view-simple.js"></script>
</body>
</html>
