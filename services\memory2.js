import mongoose from 'mongoose';
import Conversation from '../models/conversation.js';

export class HybridConversationMemory {
    constructor(options = {}) {
        this.maxSizeBytes = options.maxSizeBytes || 5 * 1024 * 1024; // 5MB default
        this.maxMessageCount = options.maxMessageCount || 20;
        this.messages = [];
        this.sessionId = options.sessionId || 'default-session';

        this.isMongoConnected = mongoose.connection.readyState === 1;
        console.log(`Memory constructor - MongoDB connection status: ${this.isMongoConnected ? 'Connected' : 'Not connected'}`);
        console.log(`MongoDB readyState: ${mongoose.connection.readyState}`);

        if (this.isMongoConnected) {
            this._loadFromMongoDB();
        }
    }

    async _loadFromMongoDB() {
        try {
            const conversation = await Conversation.findOne({ sessionId: this.sessionId });
            if (conversation && conversation.messages) {
                this.messages = conversation.messages.map(msg => ({
                    role: msg.role,
                    text: msg.text,
                    timestamp: msg.timestamp.getTime()
                }));
                console.log(`Loaded ${this.messages.length} messages from MongoDB for session ${this.sessionId}`);
            }
        } catch (error) {
            console.error('Error loading conversation from MongoDB:', error);
        }
    }

    async _saveToMongoDB() {
        if (!this.isMongoConnected) {
            console.log('MongoDB not connected, skipping save');
            return;
        }

        try {
            console.log(`Attempting to save ${this.messages.length} messages to MongoDB for session ${this.sessionId}`);

            const result = await Conversation.findOneAndUpdate(
                { sessionId: this.sessionId },
                {
                    messages: this.messages,
                    lastUpdated: new Date()
                },
                { upsert: true, new: true }
            );

            console.log(`Successfully saved ${this.messages.length} messages to MongoDB for session ${this.sessionId}`);
            console.log(`MongoDB document ID: ${result._id}`);
        } catch (error) {
            console.error('Error saving conversation to MongoDB:', error);
        }
    }

    _estimateUtf8Size(str) {
        return Buffer.byteLength(str || '', 'utf8');
    }

    _estimateMessageSize(message) {
        let size = 0;
        size += this._estimateUtf8Size(message.role);
        size += this._estimateUtf8Size(message.text);
        size += 8; // timestamp estimate
        size += 20; // fixed overhead
        return size;
    }

    async _calculateRelevanceScore(message, currentContext) {
        try {
            return this._basicTextSimilarity(message.text, currentContext);
        } catch (error) {
            console.warn('Relevance calculation error:', error);
            return 0;
        }
    }

    _basicTextSimilarity(text1, text2) {
        const words1 = new Set(text1.toLowerCase().split(/\W+/));
        const words2 = new Set(text2.toLowerCase().split(/\W+/));
        const intersection = [...words1].filter(word => words2.has(word));
        return intersection.length / Math.sqrt(words1.size * words2.size);
    }

    async prune(currentContext) {
        if (this.messages.length <= this.maxMessageCount) return;

        const scoredMessages = await Promise.all(
            this.messages.map(async (message, index) => ({
                index,
                message,
                score: await this._calculateRelevanceScore(message, currentContext)
            }))
        );

        const sortedByLeastRelevant = scoredMessages.sort((a, b) => a.score - b.score);

        const messagesToRemove = sortedByLeastRelevant
            .slice(0, this.messages.length - this.maxMessageCount)
            .map(item => item.index);

        messagesToRemove
            .sort((a, b) => b - a)
            .forEach(index => this.messages.splice(index, 1));

        if (this.isMongoConnected) {
            await this._saveToMongoDB();
        }
    }

    async checkMongoConnection() {
        if (mongoose.connection.readyState === 0) {
            console.log('MongoDB connection not yet established, waiting...');
            for (let i = 0; i < 10; i++) {
                if (mongoose.connection.readyState === 1) break;
                await new Promise(resolve => setTimeout(resolve, 500));
            }
        }

        this._updateMongoConnectionStatus();
        console.log(`MongoDB connection check: ${this.isMongoConnected ? 'Connected' : 'Not connected'}`);
        return Promise.resolve(this.isMongoConnected);
    }

    async addMessage(message, currentContext) {
        console.log(`Adding message to memory: ${message.role}: ${message.text.substring(0, 50)}...`);
        console.log(`Current session ID: ${this.sessionId}`);

        await this.checkMongoConnection();

        const messageSize = this._estimateMessageSize(message);

        if (this.messages.reduce((sum, m) => sum + this._estimateMessageSize(m), 0) + messageSize > this.maxSizeBytes) {
            await this.prune(currentContext);
        }

        this.messages.push({
            ...message,
            timestamp: Date.now()
        });

        console.log(`Current memory size: ${this.messages.length} messages`);

        if (this.isMongoConnected) {
            await this._saveToMongoDB();
        } else {
            console.log('MongoDB not connected, skipping save in addMessage');
        }
    }

    // Max context size in bytes 1MB
    async getRelevantContext(currentQuery, maxContextSize = 1024 * 1024) {
        if (this.messages.length === 0) return '';

        const scoredMessages = await Promise.all(
            this.messages.map(async (message) => ({
                message,
                score: await this._calculateRelevanceScore(message, currentQuery)
            }))
        );

        const sortedMessages = scoredMessages
            .sort((a, b) => b.score - a.score)
            .map(item => item.message);

        let contextString = '';
        const contextMessages = [];

        for (let msg of sortedMessages) {
            const msgText = `${msg.role || 'unknown'}: ${msg.text}`;
            if (contextString.length + msgText.length <= maxContextSize) {
                contextMessages.push(msg);
                contextString += msgText + '\n---\n';
            } else {
                break;
            }
        }

        return contextString;
    }

    getAllMessages() {
        return this.messages;
    }

    async clear() {
        this.messages = [];

        if (this.isMongoConnected) {
            try {
                await Conversation.findOneAndDelete({ sessionId: this.sessionId });
                console.log(`Cleared conversation from MongoDB for session ${this.sessionId}`);
            } catch (error) {
                console.error('Error clearing conversation from MongoDB:', error);
            }
        }
    }

    _updateMongoConnectionStatus() {
        const wasConnected = this.isMongoConnected;
        this.isMongoConnected = mongoose.connection.readyState === 1;

        if (wasConnected !== this.isMongoConnected) {
            console.log(`MongoDB connection status changed: ${this.isMongoConnected ? 'Connected' : 'Not connected'}`);
        }

        return this.isMongoConnected;
    }

    async setSessionId(sessionId) {
        if (this.sessionId === sessionId) return;

        console.log(`Set conversation memory session ID to: ${sessionId}`);
        this.sessionId = sessionId;
        this.messages = [];

        await this.checkMongoConnection();

        if (this.isMongoConnected) {
            await this._loadFromMongoDB();
        }
    }
}

export default HybridConversationMemory;
