<!-- Chat Area -->
<div id="chatDisplay" class="flex-1 overflow-y-auto px-4 py-4">
    <!-- Messages will be added here dynamically -->
</div>

<!-- Input Box -->
<div class="p-4 border-t border-gray-800">
    <!-- Image previews (if any) -->
    <div id="uploadedImagesContainer" class="mx-auto max-w-3xl uploaded-images-container hidden">
        <!-- Image previews will be added here dynamically -->
    </div>

    <!-- Input container -->
    <div class="mx-auto max-w-3xl input-container border border-gray-700">
        <div class="flex items-center px-4 py-3">
            <!-- Image upload button -->
            <label for="imageInput" class="p-2 text-gray-400 hover:text-gray-300 cursor-pointer">
                <i class="fas fa-image"></i>
            </label>
            <input type="file" id="imageInput" accept="image/*" class="hidden">

            <!-- Main text input -->
            <input id="textInput" type="text" placeholder="Ask Virtra" class="flex-1 bg-transparent text-white outline-none px-2">

            <!-- Send and record buttons -->
            <div class="flex items-center">
                <button id="sendButton" class="p-2 text-gray-400 hover:text-gray-300">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <div class="relative">
                    <button id="recordButton" class="p-2 text-gray-400 hover:text-gray-300">
                        <i class="fas fa-microphone"></i>
                    </button>
                    <div id="recordingIndicator" class="recording-indicator hidden">Recording...</div>
                </div>
            </div>
        </div>
    </div>

    <div class="max-w-3xl mx-auto mt-2 text-center">
        <p class="text-gray-500 text-sm">Virtra can make mistakes, so double-check it</p>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', () => {
        // Set up event listeners for the chat interface
        const textInput = document.getElementById('textInput');
        const sendButton = document.getElementById('sendButton');
        const recordButton = document.getElementById('recordButton');
        const chatDisplay = document.getElementById('chatDisplay');
        
        // Function to add a user message to the chat
        function addUserMessage(text) {
            const messageDiv = document.createElement('div');
            messageDiv.className = 'flex justify-end mb-4';
            messageDiv.innerHTML = `
                <div class="bg-blue-600 text-white rounded-lg py-2 px-4 max-w-md">
                    ${text}
                </div>
            `;
            chatDisplay.appendChild(messageDiv);
            chatDisplay.scrollTop = chatDisplay.scrollHeight;
        }
        
        // Function to add an assistant message to the chat
        function addAssistantMessage(text) {
            const messageDiv = document.createElement('div');
            messageDiv.className = 'flex mb-4';
            messageDiv.innerHTML = `
                <div class="bg-gray-700 text-white rounded-lg py-2 px-4 max-w-md">
                    ${text}
                </div>
            `;
            chatDisplay.appendChild(messageDiv);
            chatDisplay.scrollTop = chatDisplay.scrollHeight;
        }
        
        // Function to send a message to the backend
        async function sendMessage(text) {
            addUserMessage(text);
            
            try {
                const response = await fetch('/api/agent/chat', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ 
                        message: text,
                        sessionId: 'test-session'
                    })
                });
                
                const data = await response.json();
                
                // Update NLU debug panel if available
                if (data.nluAnalysis && typeof window.updateNluDebug === 'function') {
                    window.updateNluDebug(data.nluAnalysis);
                }
                
                addAssistantMessage(data.text);
                
                // Play audio if available
                if (data.audioUrl) {
                    const audio = new Audio(data.audioUrl);
                    audio.play();
                }
            } catch (error) {
                console.error('Error sending message:', error);
                addAssistantMessage('Sorry, there was an error processing your request.');
            }
        }
        
        // Event listener for send button
        sendButton.addEventListener('click', () => {
            const text = textInput.value.trim();
            if (text) {
                sendMessage(text);
                textInput.value = '';
            }
        });
        
        // Event listener for Enter key
        textInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                const text = textInput.value.trim();
                if (text) {
                    sendMessage(text);
                    textInput.value = '';
                }
            }
        });
        
        // Event listener for record button
        let mediaRecorder;
        let audioChunks = [];
        let isRecording = false;
        
        recordButton.addEventListener('click', async () => {
            if (!isRecording) {
                try {
                    const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
                    mediaRecorder = new MediaRecorder(stream);
                    
                    mediaRecorder.ondataavailable = (event) => {
                        audioChunks.push(event.data);
                    };
                    
                    mediaRecorder.onstop = async () => {
                        const audioBlob = new Blob(audioChunks, { type: 'audio/wav' });
                        await processAudio(audioBlob);
                        audioChunks = [];
                    };
                    
                    mediaRecorder.start();
                    isRecording = true;
                    
                    recordButton.innerHTML = '<i class="fas fa-microphone-slash"></i>';
                    recordButton.classList.add('text-red-500');
                    document.getElementById('recordingIndicator').classList.remove('hidden');
                } catch (error) {
                    console.error('Error accessing microphone:', error);
                    alert('Error accessing microphone: ' + error.message);
                }
            } else {
                mediaRecorder.stop();
                isRecording = false;
                
                recordButton.innerHTML = '<i class="fas fa-microphone"></i>';
                recordButton.classList.remove('text-red-500');
                document.getElementById('recordingIndicator').classList.add('hidden');
            }
        });
        
        // Function to process audio
        async function processAudio(audioBlob) {
            try {
                // Create form data
                const formData = new FormData();
                formData.append('audio', audioBlob);
                
                // Send to transcription API
                const transcribeResponse = await fetch('/api/transcribe', {
                    method: 'POST',
                    body: formData
                });
                
                if (!transcribeResponse.ok) {
                    throw new Error('Transcription failed');
                }
                
                const transcribeData = await transcribeResponse.json();
                const transcript = transcribeData.text;
                
                // Send the transcribed text to the agent
                if (transcript) {
                    sendMessage(transcript);
                }
            } catch (error) {
                console.error('Error processing audio:', error);
                alert('Error processing audio: ' + error.message);
            }
        }
    });
</script>
