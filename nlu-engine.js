// Ensure you have the @google-generative-ai package installed:
// npm install @google-generative-ai
// Ensure you have set your GOOGLE_API_KEY environment variable.
// export GOOGLE_API_KEY="YOUR_API_KEY"

import { GoogleGenerativeAI } from "@google/generative-ai";
import dotenv from 'dotenv';

dotenv.config();

// In-file configuration.  Consider moving this to a separate config file for larger projects.
const llmNluConfig = {
    modelName: "gemini-1.5-flash", // Or the same as your main model.  Choose a suitable model.
    generationConfig: {
        temperature: 0.3, // Lower temperature for more deterministic NLU output
        topK: 20,
        topP: 0.8,
        maxOutputTokens: 512, // Increased for potential explanations
    },
};

// Initialize the Google AI model for NLU
const genAI_nlu = new GoogleGenerativeAI(process.env.GOOGLE_API_KEY);
const nluModel = genAI_nlu.getGenerativeModel({
    model: llmNluConfig.modelName,
    generationConfig: llmNluConfig.generationConfig,
});

// Define custom intents.  Expand this list as needed.
const customIntents = [
    "book_flight",
    "get_weather",
    "play_music",
    "set_reminder",
    "get_news",
    "query_time",
    "translate",
    "express_positive_emotion",
    "express_dissatisfaction",
    "provide_feedback",
    "set_alarm",
    "request_information",
    "make_a_reservation", 
    "order_food" 

];

// Construct the intent instructions string for the prompt.
const intentInstructions = `The possible intents are: ${customIntents.join(", ")}.`;

//  The prompt template.  This is well-structured and clear.
const nluPromptTemplate = `You are a detailed linguistic analysis engine. Your task is to read the user's message and accurately determine its sentiment, the primary intent (from the provided list), and identify any relevant named entities with as much specificity as possible. Return your analysis as a JSON object with the following keys:
- "sentiment": The overall sentiment of the message ("positive", "negative", "neutral", or "mixed").
- "intent": A concise label describing the user's primary goal or request from the following list: ${intentInstructions}. If the intent is unclear, use "unknown".
- "entities": An object where keys are the entity types (e.g., "location", "date", "product", "person", "team") and values are arrays of the identified entities. Be specific (e.g., "Winter Haven, Florida").
- "confidence": An object containing your confidence score (between 0 and 1) for each of "sentiment", "intent", and "entities".
- "explanation": A brief explanation for the confidence score assigned to each of "sentiment", "intent", and "entities".
Consider the current context: Current location is Winter Haven, Florida, United States; Current time is Friday, April 11, 2025 at 10:32 AM EDT.
Analyze the following user message:
""" {{user_message}} """
Respond ONLY with a valid JSON object.`;


// Asynchronous function to analyze user messages.  Error handling is robust.
async function analyze(userMessage) {
    const prompt = nluPromptTemplate.replace("{{user_message}}", userMessage);
    try {
        const result = await nluModel.generateContent([prompt]);
        const responseText = result.response?.candidates?.[0]?.content?.parts?.[0]?.text;

        if (responseText) {
            let jsonString = responseText.trim();

            // Remove Markdown-style code block formatting like ```json ... ```
            if (jsonString.startsWith("```")) {
                jsonString = jsonString.replace(/^```(?:json)?/, '').replace(/```$/, '').trim();
            }

            try {
                const nluResult = JSON.parse(jsonString);
                return nluResult;
            } catch (error) {
                console.error("Primary JSON parse failed. Attempting fallback extraction.");
                // Try extracting JSON block between first and last curly brace
                const firstBrace = jsonString.indexOf('{');
                const lastBrace = jsonString.lastIndexOf('}');
                if (firstBrace !== -1 && lastBrace !== -1) {
                    const fallback = jsonString.substring(firstBrace, lastBrace + 1);
                    try {
                        return JSON.parse(fallback);
                    } catch (fallbackError) {
                        console.error("Fallback parse also failed:", fallbackError, fallback);
                        return null;
                    }
                }
                return null;
            }
        } else {
            console.warn("Gemini NLU returned an empty response.");
            return null;
        }
    } catch (error) {
        console.error("Error calling Gemini for NLU:", error);
        return null;
    }
}


// Example test statements.  This is a good set of diverse examples.
async function runTests() {
    const testStatements = [
        "I'm so happy the weather is great today!",
        "This movie was absolutely terrible and a waste of money.",
        "What time is it in Winter Haven?",
        "Book a flight from Winter Haven to New York on April 15th, 2025.",
        "Tell me the latest news about the Florida Gators football team.",
        "The coffee is okay, but the service at Starbucks was slow.",
        "Remind me to buy groceries tomorrow morning at 8 AM.",
        "Set an alarm for 7:00 AM on Saturday.",
        "Who is the current president of the United States?",
        "Translate 'hello' to Spanish.",
        "Is the Orlando Magic playing tonight?",
        "What's the weather like in London?", // Expecting the LLM to infer London, UK as it's more common
        "I'd like to make a reservation for dinner tonight.", //Testing added intent
        "Order me a pizza with pepperoni and extra cheese." //Testing added intent
    ];

    for (const statement of testStatements) {
        console.log(`\n--- Analyzing: "${statement}" ---`);
        const analysisResult = await analyze(statement);
        console.log("Analysis Result:", analysisResult);
    }
}

runTests();