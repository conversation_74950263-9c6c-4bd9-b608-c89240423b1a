{"name": "voice-agent", "version": "1.0.0", "description": "Voice-enabled AI agent with speech-to-text and text-to-speech capabilities", "main": "app.js", "type": "module", "scripts": {"start": "node app.js", "dev": "nodemon app.js", "test-search": "node scripts/test-internet-search.js", "test-enhanced-search": "node scripts/test-enhanced-search.js", "test-email": "node scripts/test-email-tools.js", "test-email-simple": "node scripts/test-email-simple.js", "gmail-auth": "node scripts/test-gmail-auth.js", "create-token": "node scripts/create-token.js"}, "keywords": ["voice", "agent", "speech-to-text", "text-to-speech", "ai"], "author": "", "license": "MIT", "dependencies": {"@google-cloud/speech": "^6.3.0", "@google-cloud/text-to-speech": "^5.8.1", "@google/genai": "^0.12.0", "@google/generative-ai": "^0.24.0", "@pinecone-database/pinecone": "^5.1.1", "cheerio": "^1.0.0", "cors": "^2.8.5", "dotenv": "^16.0.3", "express": "^4.18.2", "googleapis": "^148.0.0", "mailparser": "^3.7.2", "mongoose": "^7.2.0", "multer": "^1.4.5-lts.1", "node-html-parser": "^7.0.1", "open": "^10.1.0", "server-destroy": "^1.0.1"}, "devDependencies": {"nodemon": "^3.0.3"}}