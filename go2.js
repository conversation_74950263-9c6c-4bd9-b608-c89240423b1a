import { google } from "googleapis";
import path from 'path';
import { fileURLToPath } from 'url';
import { simpleParser } from 'mailparser';
import { GoogleGenerativeAI } from "@google/generative-ai";
import dotenv from 'dotenv';
import fs from "fs"; // Still needed for token file reading

dotenv.config();

// Get the directory name
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Path to token file
const TOKEN_PATH = path.join(__dirname, 'token.json'); // Simplified path

// Create token.json if it doesn't exist
if (!fs.existsSync(TOKEN_PATH)) {
    console.log('Token file not found. Creating a new one...');

    // Create a minimal token with the required fields
    const minimalToken = {
        access_token: 'ya29.dummy-access-token',
        refresh_token: '1//dummy-refresh-token',
        scope: 'https://www.googleapis.com/auth/gmail.readonly https://www.googleapis.com/auth/gmail.modify',
        token_type: 'Bearer',
        expiry_date: new Date().getTime() + 3600000 // 1 hour from now
    };

    // Save the token
    fs.writeFileSync(TOKEN_PATH, JSON.stringify(minimalToken));
    console.log(`Created dummy token at ${TOKEN_PATH}`);
}

// Initialize the OAuth client using token.json file
function getOAuthClient() {
    // Check if token file exists
    if (!fs.existsSync(TOKEN_PATH)) {
        throw new Error("Token file not found. Please authenticate first.");
    }

    // Read token data
    const tokenData = JSON.parse(fs.readFileSync(TOKEN_PATH));

    // Create OAuth client with the actual credentials
    const oAuth2Client = new google.auth.OAuth2(
        '122591791863-e7o6l17e9k3i4e6kogr96t99hdffvkma.apps.googleusercontent.com',   // Client ID
        '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',                                           // Client Secret
        'http://localhost:3081/oauth2callback'                                           // Redirect URI
    );

    // Set the credentials from the token file
    oAuth2Client.setCredentials(tokenData);

    return oAuth2Client;
}

/**
 * Fetch and parse unread emails from Gmail
 * @param {number} maxResults - Maximum number of emails to fetch
 * @returns {Array} - Array of parsed email objects
 */
async function getUnreadEmails(maxResults = 5) {
    try {
        const auth = getOAuthClient();
        const gmail = google.gmail({ version: "v1", auth });

        const res = await gmail.users.messages.list({
            userId: "me",
            labelIds: ["INBOX"],
            q: "is:unread",
            maxResults: maxResults,
        });

        if (!res.data.messages) {
            return []; // Return empty array if no unread emails
        }

        const emails = [];
        for (const msg of res.data.messages) {
            const email = await gmail.users.messages.get({
                userId: "me",
                id: msg.id,
                format: "raw" // Get raw message to parse with mailparser
            }).catch(err => {
                console.error('Error fetching email:', err);
                return { data: { raw: '' } };
            });

            // Decode the raw message
            const rawEmail = Buffer.from(email.data.raw, 'base64').toString('utf8');

            // Parse the email using mailparser
            const parsedEmail = await simpleParser(rawEmail);

            emails.push({
                id: msg.id,
                from: parsedEmail.from?.text || "N/A",
                to: parsedEmail.to?.text || "N/A",
                subject: parsedEmail.subject || "N/A",
                date: parsedEmail.date || new Date(),
                text: parsedEmail.text || "",
                html: parsedEmail.html || "",
                textAsHtml: parsedEmail.textAsHtml || "",
                body: parsedEmail.html || parsedEmail.text || "",
                attachments: parsedEmail.attachments || [],
                headerLines: parsedEmail.headerLines || [],
                headers: parsedEmail.headers || {},
                messageId: parsedEmail.messageId || "",
                unread: email.data.labelIds.includes("UNREAD")
            });
        }

        return emails;
    } catch (error) {
        console.error("Error fetching emails:", error);
        return []; // Return empty array on error
    }
}

// Initialize Gemini
const genAI = new GoogleGenerativeAI(process.env.GOOGLE_API_KEY);
const preparationModel = genAI.getGenerativeModel({ model: "gemini-2.0-flash-lite" }); // Choose appropriate model

// Prompt Template for the Preparation Agent
const preparationPrompt = `You are an AI agent designed to prepare email data for an importance scoring LLM.
Your task is to take the raw email data (including the full text), the output of a Natural Language Understanding (NLU) analysis, user context, and importance scoring instructions and structure them into a concise JSON object that will be given to the scoring LLM. **Accuracy is paramount. Do not invent or assume information not present in the input.**

Here's the raw email data:
\`\`\`json
{{email_data}}\`\`\`

Here's the NLU analysis of the email:
\`\`\`json
{{nlu_analysis}}\`\`\`

Here's the user's context:
\`\`\`json
{{user_context}}\`\`\`

Here are the importance scoring instructions that the scoring LLM will use:
\`\`\`json
{{importance_scoring_instructions}}\`\`\`

Based on all of the information above, create a JSON object with the following structure:

\`\`\`json
{
  "email_summary": "(A concise summary of the email's content, incorporating key information from the full text)",
  "sender_importance": "(Sender's importance level: 'low', 'medium', or 'high')",
  "intent": "(The primary intent of the email, potentially refined based on the full text)",
  "sentiment": "(The sentiment of the email: 'positive', 'negative', 'neutral', or 'mixed', potentially influenced by the full text)",
  "urgency_indicators": "(An array of urgency indicators, extracted accurately from the NLU analysis and the full text. Only include 'payment' indicators if explicitly mentioned with a date.)",
  "has_attachment": "(Boolean: true if the email has an attachment, false otherwise)",
  "user_preferences": "(Relevant user preferences that might affect importance)",
  "scoring_instructions": "(Copy of the importance scoring instructions)",
  "reasoning_prompt": "(A short prompt to guide the scoring LLM's reasoning, referencing specific details from the email text if relevant)"
}
\`\`\``;

/**
 * Prepares email data for scoring by an LLM using @google/generative-ai.
 * @param {object} email - The raw email data object.
 * @param {object} nluAnalysis - The NLU analysis result from the first LLM.
 * @param {object} userContext - The user's context information.
 * @param {object} importanceScoringInstructions - The instructions for scoring email importance.
 * @returns {object|null} - A JSON object ready for the scoring LLM, or null on error.
 */
async function prepareEmailForScoring(email, nluAnalysis, userContext, importanceScoringInstructions) {
    try {
        const emailData = {
            from: email.from,
            to: email.to,
            subject: email.subject,
            date: email.date,
            text: email.text,
            html: email.html,
            textAsHtml: email.textAsHtml,
            body: email.body,
            attachments: email.attachments,
            headerLines: email.headerLines,
            headers: email.headers,
            messageId: email.messageId
        };

        const prompt = preparationPrompt
            .replace("{{email_data}}", JSON.stringify(emailData, null, 2))
            .replace("{{nlu_analysis}}", JSON.stringify(nluAnalysis, null, 2))
            .replace("{{user_context}}", JSON.stringify(userContext, null, 2))
            .replace("{{importance_scoring_instructions}}", JSON.stringify(importanceScoringInstructions, null, 2));

        const result = await preparationModel.generateContent([prompt]);
        const responseText = result.response?.candidates?.[0]?.content?.parts?.[0]?.text;

        if (responseText) {
            try {
                // Attempt to parse the response as JSON
                let jsonString = responseText.trim();
                if (jsonString.startsWith("```")) {
                    jsonString = jsonString.replace(/^```(?:json)?/, '').replace(/```$/, '').trim();
                }
                const preparedData = JSON.parse(jsonString);
                return preparedData;
            } catch (parseError) {
                console.error("Error parsing LLM response:", parseError, responseText);
                return null;
            }
        } else {
            console.warn("Preparation LLM returned empty response.");
            return null;
        }

    } catch (error) {
        console.error("Error preparing email for scoring:", error);
        return null;
    }
}

/**
 * Formats urgency indicators into a structured array.
 * @param {Array<any>} urgencyIndicators - Array of urgency indicators (can be mixed types).
 * @returns {Array<object>} - Array of structured urgency indicator objects.
 */
function formatUrgencyIndicators(urgencyIndicators) {
    const formattedIndicators = [];
    if (Array.isArray(urgencyIndicators)) {
        for (const indicator of urgencyIndicators) {
            if (typeof indicator === 'string') {
                const dateMatch = indicator.match(/(\d{4}-\d{2}-\d{2})/); // Match YYYY-MM-DD
                if (dateMatch) {
                    formattedIndicators.push({ type: "date", date: dateMatch[0] });
                } else if (indicator.toLowerCase().includes("payment")) {
                    formattedIndicators.push({ type: "payment" });
                } else if (indicator.toLowerCase().includes("action needed")) {
                    formattedIndicators.push({ type: "action_needed" });
                } else {
                    formattedIndicators.push({ type: "general", indicator });
                }
            } else if (typeof indicator === 'object' && indicator !== null) { // Handle structured objects
                if (indicator.type === 'payment') {
                    if (indicator.date) {
                        formattedIndicators.push({ type: 'payment', date: indicator.date });
                    } else {
                        console.warn("Payment indicator without date:", indicator);
                        formattedIndicators.push({ type: 'payment', date: null }); // Or handle this differently
                    }
                } else {
                    formattedIndicators.push({ type: 'structured', value: indicator }); // Generic structured
                }
            } else {
                console.warn("Unexpected urgency indicator type:", indicator);
                formattedIndicators.push({ type: "unknown", value: indicator }); // Handle other types
            }
        }
    } else {
        console.warn("urgencyIndicators is not an array:", urgencyIndicators);
    }
    return formattedIndicators;
}

// Update the main function to show email content and prepare for scoring (without saving files)
async function main() {
    try {
        const emails = await getUnreadEmails(5).catch(err => {
            console.error('Error getting unread emails:', err);
            return [];
        }); // Get 5 emails
        const currentTime = new Date('2025-04-11T19:36:00-04:00'); // Explicitly set current time

        if (emails.length > 0) {
            console.log(`Unread Emails (Current time: ${currentTime.toLocaleString()}):`);
            for (const email of emails) {
                console.log(`\n- From: ${email.from}`);
                console.log(`  Subject: ${email.subject}`);
                console.log(`  Date: ${email.date}`);

                const textContent = email.text || "No text content available";
                console.log(`  Text Preview: ${textContent.substring(0, 150)}...`);

                // *** UPDATED SAMPLE NLU ANALYSIS ***
                let sampleNluAnalysis;
                if (email.subject.includes("Payment is Due")) {
                    sampleNluAnalysis = {
                        sentiment: { label: "neutral", confidence: 0.9 },
                        intent: { label: "payment reminder", confidence: 0.95 },
                        entities: { "date": ["April 12, 2025"] },
                        urgency_indicators: [{ type: 'payment', date: '2025-04-12' }],
                        keywords: ["payment", "due"]
                    };
                } else if (email.subject.includes("Coinbase User Agreement")) {
                    sampleNluAnalysis = {
                        sentiment: { label: "neutral", confidence: 0.8 },
                        intent: { label: "information", confidence: 0.9 },
                        entities: { "date": ["May 15, 2025"] },
                        urgency_indicators: [],
                        keywords: ["update", "agreement", "important"]
                    };
                } else if (email.subject.includes("subscribed to you")) {
                    sampleNluAnalysis = {
                        sentiment: { label: "neutral", confidence: 0.9 },
                        intent: { label: "notification", confidence: 0.95 },
                        entities: {},
                        urgency_indicators: [],
                        keywords: ["subscribed"]
                    };
                } else if (email.subject.includes("Security alert")) {
                    sampleNluAnalysis = {
                        sentiment: { label: "negative", confidence: 0.85 },
                        intent: { label: "security alert", confidence: 0.98 },
                        entities: {},
                        urgency_indicators: ["important"],
                        keywords: ["security", "alert", "access"]
                    };
                } else if (email.subject.includes("Creator Newsletter")) {
                    sampleNluAnalysis = {
                        sentiment: { label: "neutral", confidence: 0.8 },
                        intent: { label: "newsletter", confidence: 0.92 },
                        entities: { "date": ["April"] },
                        urgency_indicators: [],
                        keywords: ["newsletter", "updates"]
                    };
                } else {
                    sampleNluAnalysis = { // Default case
                        sentiment: { label: "neutral", confidence: 0.7 },
                        intent: { label: "unknown", confidence: 0.5 },
                        entities: {},
                        urgency_indicators: [],
                        keywords: []
                    };
                }

                const sampleUserContext = {
                    current_time: currentTime.toISOString(),
                    current_location: "Winter Haven, Florida, United States",
                    user_preferences: {
                        prioritize_senders: ["<EMAIL>"],
                        keywords_high_priority: ["urgent", "important"],
                        categories_of_interest: ["personal", "finance"]
                    }
                };

                const sampleImportanceScoringInstructions = {
                    high_priority_indicators: [
                        "urgency_indicators.some(u => u.type == 'payment' && new Date(u.date).getTime() < new Date(user_context.current_time).getTime() + 24 * 60 * 60 * 1000)",
                        "sender_importance == 'high' || keywords.includes('urgent')"
                    ],
                    medium_priority_indicators: [
                        "sender_importance == 'medium' || keywords.includes('important')"
                    ],
                    weighting_factors: {
                        sender: 0.2,
                        urgency: 0.5,
                        content: 0.3
                    },
                    scoring_logic_description: "Score based on sender, urgency (emphasizing imminent deadlines), and content keywords."
                };

                const preparedDataForScoring = await prepareEmailForScoring(
                    email,
                    sampleNluAnalysis,
                    sampleUserContext,
                    sampleImportanceScoringInstructions
                );

                if (preparedDataForScoring) {
                    if (preparedDataForScoring.urgency_indicators && Array.isArray(preparedDataForScoring.urgency_indicators)) {
                        preparedDataForScoring.urgency_indicators = formatUrgencyIndicators(preparedDataForScoring.urgency_indicators);
                    }

                    preparedDataForScoring.reasoning_prompt = `The current time is ${currentTime.toLocaleString()}. This email is a ${preparedDataForScoring.intent} from ${email.from} with subject: "${email.subject}". The keywords are: ${sampleNluAnalysis.keywords.join(', ')}. Considering any urgency indicators and the provided scoring logic, evaluate its importance.`;

                    console.log("\n--- Prepared Data for Scoring LLM ---");
                    console.log(JSON.stringify(preparedDataForScoring, null, 2));
                } else {
                    console.warn(`Failed to prepare data for scoring email ${email.id}`);
                }
            }
        } else {
            console.log("No unread emails found.");
        }
    } catch (error) {
        console.error("Error in main:", error);
    }
}

main();