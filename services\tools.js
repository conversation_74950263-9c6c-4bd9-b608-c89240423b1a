/**
 * Tools Service
 *
 * Defines the tools available to the AI agent and provides functions for executing them.
 */

// Import the enhanced search functionality
import { performEnhancedSearch } from './enhancedSearch.js';

// Import email functionality
import { getUnreadEmails, searchEmails, getEmailDetails, mark<PERSON><PERSON><PERSON>, getLabels } from '../email.js';

// Define the tools available to the agent
export const tools = [
  {
    name: "wristwatch",
    description: "Your luxury Jaeger-LeCoultre Calibre 822 wristwatch in Pink Gold 750/1000 (18 carats). While appearing to be a traditional mechanical watch with a hand-wound movement, it has subtle AI integration by Virtron Labs that provides accurate time information. It features a classic round case with a silver-toned dial, gold hour markers, and a hand-stitched alligator leather strap.",
    instructions: "Use this tool ONLY when the user explicitly asks about the current time, date, or your watch. Do not mention the time in every response.",
    parameters: []
  },
  {
    name: "searxng_search",
    description: "Searches the internet using SearXNG to retrieve real-time information from the web. This tool uses a local Docker instance of SearXNG with fallback to public instances and provides up-to-date information about current events, news, and other topics. The search results are processed using Cheerio for better HTML parsing and content extraction.",
    instructions: "Use this tool when the user asks about current events, news, or any information that may change over time and requires up-to-date data from the internet. Also use it when the user explicitly asks you to search for something.",
    parameters: [
      {
        name: "query",
        type: "string",
        description: "The search query to use. Be specific and concise for better results.",
        required: true,
      },
    ],
  },
  {
    name: "email_unread",
    description: "Retrieves unread emails from the user's Gmail inbox. This tool provides access to the user's email account to check for new messages, count emails, or categorize them by importance.",
    instructions: "Use this tool when the user asks about their unread emails, new messages, wants to check their inbox, or needs to know how many unread emails they have. You can retrieve all emails, count them, or categorize them by importance without displaying all details.",
    parameters: [
      {
        name: "limit",
        type: "number",
        description: "Maximum number of unread emails to retrieve. Set to a high number (e.g., 100) to retrieve all emails, or leave unspecified to use the default.",
        required: false,
      },
      {
        name: "count_only",
        type: "boolean",
        description: "If true, only returns the count of unread emails without retrieving the full details. Useful when the user just wants to know how many unread emails they have.",
        required: false,
      },
      {
        name: "categorize",
        type: "boolean",
        description: "If true, categorizes emails by importance (critical, important, normal, spam) based on sender and subject analysis.",
        required: false,
      },
    ],
  },
  {
    name: "email_search",
    description: "Searches the user's Gmail account for specific emails based on a query. This tool can find emails by sender, subject, content, or other criteria.",
    instructions: "Use this tool when the user wants to find specific emails in their account.",
    parameters: [
      {
        name: "query",
        type: "string",
        description: "The search query to use. Can include Gmail search operators like 'from:', 'to:', 'subject:', etc.",
        required: true,
      },
      {
        name: "limit",
        type: "number",
        description: "Maximum number of emails to retrieve. Default is 10.",
        required: false,
      },
    ],
  },
  {
    name: "email_read",
    description: "Retrieves the full content of a specific email by its ID. This tool provides access to the complete email including headers and body.",
    instructions: "Use this tool when the user wants to read the content of a specific email that was found using email_unread or email_search.",
    parameters: [
      {
        name: "id",
        type: "string",
        description: "The ID of the email to retrieve.",
        required: true,
      },
    ],
  },
  {
    name: "email_mark_read",
    description: "Marks a specific email as read. This tool updates the status of an email in the user's Gmail account.",
    instructions: "Use this tool when the user wants to mark a specific email as read after viewing it.",
    parameters: [
      {
        name: "id",
        type: "string",
        description: "The ID of the email to mark as read.",
        required: true,
      },
    ],
  },
  {
    name: "email_find",
    description: "Finds an email by subject and/or sender. Use this to get the ID of an email when you only know the subject and sender.",
    parameters: [
      {
        name: "subject",
        type: "string",
        description: "The subject of the email to find.",
        required: false,
      },
      {
        name: "sender",
        type: "string",
        description: "The sender of the email to find.",
        required: false,
      }
    ],
    async execute({ subject, sender }) {
      try {
        // Import the findEmailBySubjectAndSender function
        const { findEmailBySubjectAndSender } = await import('../email.js');

        // Call the function
        const result = await findEmailBySubjectAndSender(subject, sender);
        return result;
      } catch (error) {
        console.error("Error executing email_find tool:", error);
        return {
          success: false,
          message: `Error finding email: ${error.message}`
        };
      }
    }
  },
  {
    name: "email_find_and_mark_read",
    description: "Finds an email by subject and/or sender and marks it as read. IMPORTANT: Use this tool when the user wants to mark a specific email as read.",
    instructions: "Use this tool when the user explicitly asks to mark an email as read. You MUST use this tool whenever the user wants to mark an email as read, even if they don't provide the exact ID. At least one parameter (subject or sender) must be provided.",
    parameters: [
      {
        name: "subject",
        type: "string",
        description: "The subject (or part of the subject) of the email to find and mark as read.",
        required: false,
      },
      {
        name: "sender",
        type: "string",
        description: "The sender (name or email address) of the email to find and mark as read.",
        required: false,
      }
    ],
    async execute({ subject, sender }) {
      try {
        console.log(`Executing email_find_and_mark_read tool with subject: "${subject}", sender: "${sender}"`);

        // Import the findAndMarkAsRead function
        const { findAndMarkAsRead } = await import('../email.js');

        // Call the function
        const result = await findAndMarkAsRead(subject, sender);

        // Log the result for debugging
        console.log(`Result of findAndMarkAsRead:`, result);

        return result;
      } catch (error) {
        console.error("Error executing email_find_and_mark_read tool:", error);
        return {
          success: false,
          message: `Error finding and marking email as read: ${error.message}`
        };
      }
    }
  },
  {
    name: "image_analysis",
    description: "Analyzes images uploaded by the user to provide descriptions and insights about the visual content. This tool can identify objects, scenes, text, and other elements in images.",
    instructions: "This tool is automatically used when the user uploads an image. You don't need to explicitly call it.",
    parameters: []
  },
  {
    name: "text_to_speech",
    description: "Converts your text responses to natural-sounding speech that is played back to the user. The speech output is cleaned to remove markdown, URLs, and improve pronunciation of technical terms.",
    instructions: "This tool is automatically used for all your responses. You don't need to explicitly call it. When writing responses that will be spoken, avoid using markdown, special characters, or complex formatting.",
    parameters: []
  },
  {
    name: "speech_to_text",
    description: "Converts the user's spoken words to text using advanced speech recognition. This allows users to interact with you through voice input.",
    instructions: "This tool is automatically used when the user speaks. You don't need to explicitly call it.",
    parameters: []
  }
];

/**
 * Get the current date and time information
 * @returns {object} - Object containing date and time information
 */
export function getCurrentDateTime() {
  const now = new Date();

  // Format the date in a readable way
  const dateOptions = { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' };
  const timeOptions = { hour: '2-digit', minute: '2-digit', second: '2-digit', hour12: true };

  const formattedDate = now.toLocaleDateString('en-US', dateOptions);
  const formattedTime = now.toLocaleTimeString('en-US', timeOptions);

  // Get timezone information
  const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
  const timezoneOffset = now.getTimezoneOffset();
  const timezoneOffsetHours = Math.abs(Math.floor(timezoneOffset / 60));
  const timezoneOffsetMinutes = Math.abs(timezoneOffset % 60);
  const timezoneString = `UTC${timezoneOffset <= 0 ? '+' : '-'}${timezoneOffsetHours.toString().padStart(2, '0')}:${timezoneOffsetMinutes.toString().padStart(2, '0')}`;

  return {
    date: formattedDate,
    time: formattedTime,
    timezone,
    timezoneOffset: timezoneString,
    iso8601: now.toISOString(),
    unix: Math.floor(now.getTime() / 1000),
    year: now.getFullYear(),
    month: now.getMonth() + 1, // JavaScript months are 0-indexed
    day: now.getDate(),
    hour: now.getHours(),
    minute: now.getMinutes(),
    second: now.getSeconds(),
    millisecond: now.getMilliseconds(),
    weekday: dateOptions.weekday === 'long' ? now.toLocaleDateString('en-US', { weekday: 'long' }) : now.getDay(),
    message: `According to my Jaeger-LeCoultre Calibre 822 wristwatch, it is currently ${formattedTime} on ${formattedDate} (${timezoneString}).`
  };
}

/**
 * Perform a search using SearXNG
 * @param {object} args - Arguments for the search, including the query
 * @returns {Promise<object>} - The search results
 */
async function performSearxNGSearch(args) {
  try {
    // Check if query is provided
    if (!args.query) {
      return { error: 'No search query provided' };
    }

    console.log(`Performing search for: ${args.query}`);

    // Use the enhanced search functionality
    const searchResults = await performEnhancedSearch(args.query);

    // Format the results for the AI
    return {
      query: args.query,
      results: searchResults.results.map(result => {
        // Find the content for this URL
        const contentItem = searchResults.content.find(c => c.includes(result.url));

        // Extract title from content if available
        let title = 'No title';
        if (contentItem) {
          const titleMatch = contentItem.match(/Title: ([^\n]+)/);
          if (titleMatch && titleMatch[1]) {
            title = titleMatch[1];
          }
        }

        return {
          title: title,
          url: result.url,
          content: contentItem ? contentItem.replace(/^Source: [^\n]+\n/, '').replace(/^Title: [^\n]+\n/, '').substring(0, 200) + '...' : 'No content available',
          engine: 'searxng'
        };
      }),
      number_of_results: searchResults.results.length
    };
  } catch (error) {
    console.error('Error performing search:', error);
    return { error: `Failed to perform search: ${error.message}` };
  }
}

/**
 * Execute a tool based on its name and arguments
 * @param {string} toolName - The name of the tool to execute
 * @param {object} args - Arguments for the tool
 * @returns {Promise<object>} - The result of the tool execution
 */
export async function executeTool(toolName, args = {}) {
  // Find the tool definition
  const tool = tools.find(t => t.name === toolName);

  // If the tool has a custom execute method, use it
  if (tool && typeof tool.execute === 'function') {
    console.log(`Executing custom tool: ${toolName}`);
    return tool.execute(args);
  }

  // Otherwise, use the switch statement for standard tools
  switch (toolName) {
    case "wristwatch":
      return getCurrentDateTime();

    // For backward compatibility
    case "datetime":
      return getCurrentDateTime();

    case "searxng_search":
      return performSearxNGSearch(args);

    case "email_unread":
      return getUnreadEmails(
        args.limit || 5,
        args.count_only || false,
        args.categorize || false
      );

    case "email_search":
      return searchEmails(args.query, args.limit || 10);

    case "email_read":
      return getEmailDetails(args.id);

    case "email_mark_read":
      return markAsRead(args.id);

    case "email_find":
      try {
        const { findEmailBySubjectAndSender } = await import('../email.js');
        return findEmailBySubjectAndSender(args.subject, args.sender);
      } catch (error) {
        console.error("Error executing email_find tool:", error);
        return {
          success: false,
          message: `Error finding email: ${error.message}`
        };
      }

    case "email_find_and_mark_read":
      try {
        console.log(`Executing email_find_and_mark_read tool with subject: "${args.subject}", sender: "${args.sender}"`);
        const { findAndMarkAsRead } = await import('../email.js');
        const result = await findAndMarkAsRead(args.subject, args.sender);
        console.log(`Result of findAndMarkAsRead:`, result);
        return result;
      } catch (error) {
        console.error("Error executing email_find_and_mark_read tool:", error);
        return {
          success: false,
          message: `Error finding and marking email as read: ${error.message}`
        };
      }

    default:
      return { error: `Tool "${toolName}" not found or cannot be directly called.` };
  }
}

export default {
  tools,
  executeTool,
  getCurrentDateTime
};
