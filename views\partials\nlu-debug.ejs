<!-- NLU Debug Panel (hidden by default) -->
<div id="nluDebugPanel" class="fixed bottom-0 right-0 bg-gray-900 border border-gray-700 rounded-tl-lg shadow-lg p-4 max-w-md max-h-96 overflow-auto hidden">
  <div class="flex justify-between items-center mb-2">
    <h3 class="text-white font-bold">NLU Analysis</h3>
    <button id="closeNluDebug" class="text-gray-400 hover:text-white">
      <i class="fas fa-times"></i>
    </button>
  </div>
  <div id="nluDebugContent" class="text-sm text-gray-300">
    <p class="text-gray-400 italic">No NLU data available</p>
  </div>
</div>

<script>
  // Toggle NLU debug panel with Alt+N
  document.addEventListener('keydown', (e) => {
    if (e.altKey && e.key === 'n') {
      const panel = document.getElementById('nluDebugPanel');
      panel.classList.toggle('hidden');
    }
  });
  
  // Close button
  document.getElementById('closeNluDebug').addEventListener('click', () => {
    document.getElementById('nluDebugPanel').classList.add('hidden');
  });
  
  // Function to update NLU debug panel
  window.updateNluDebug = function(nluData) {
    const content = document.getElementById('nluDebugContent');
    
    if (!nluData) {
      content.innerHTML = '<p class="text-gray-400 italic">No NLU data available</p>';
      return;
    }
    
    let html = `
      <div class="mb-2">
        <span class="text-blue-400 font-semibold">Intent:</span> 
        <span class="text-white">${nluData.intent}</span>
        <span class="text-gray-500 text-xs ml-2">(Confidence: ${(nluData.confidence?.intent || 0).toFixed(2)})</span>
      </div>
      
      <div class="mb-2">
        <span class="text-blue-400 font-semibold">Sentiment:</span> 
        <span class="text-white">${nluData.sentiment}</span>
        <span class="text-gray-500 text-xs ml-2">(Confidence: ${(nluData.confidence?.sentiment || 0).toFixed(2)})</span>
      </div>
    `;
    
    if (nluData.entities && Object.keys(nluData.entities).length > 0) {
      html += '<div class="mb-2"><span class="text-blue-400 font-semibold">Entities:</span></div>';
      html += '<ul class="list-disc pl-5 mb-2">';
      
      for (const [entityType, entities] of Object.entries(nluData.entities)) {
        if (Array.isArray(entities) && entities.length > 0) {
          html += `<li><span class="text-gray-300">${entityType}:</span> <span class="text-white">${entities.join(', ')}</span></li>`;
        }
      }
      
      html += '</ul>';
    }
    
    if (nluData.explanation) {
      html += `
        <div class="mt-3 pt-2 border-t border-gray-700">
          <span class="text-blue-400 font-semibold">Explanation:</span>
          <p class="text-gray-400 text-xs mt-1">${nluData.explanation}</p>
        </div>
      `;
    }
    
    content.innerHTML = html;
  };
</script>
