import fs from "fs";
import { google } from "googleapis";
import path from 'path';
import { fileURLToPath } from 'url';
import { simpleParser } from 'mailparser';

// Get the directory name
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Path to token file
const TOKEN_PATH = path.join(__dirname, 'token.json');

// Initialize the OAuth client using token.json file
function getOAuthClient() {
    if (!fs.existsSync(TOKEN_PATH)) {
        throw new Error("Token file not found. Please authenticate first.");
    }
    const tokenData = JSON.parse(fs.readFileSync(TOKEN_PATH));
    const oAuth2Client = new google.auth.OAuth2(
        '122591791863-e7o6l17e9k3i4e6kogr96t99hdffvkma.apps.googleusercontent.com',
        '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',
        'http://localhost:3081/oauth2callback'
    );
    oAuth2Client.setCredentials(tokenData);
    return oAuth2Client;
}

/**
 * Fetch unread emails from Gmail and extract comprehensive metadata.
 * @param {number} maxResults - Maximum number of emails to fetch
 * @returns {Array} - Array of email objects with comprehensive metadata.
 */
async function getUnreadEmails(maxResults = 5) {
    try {
        const auth = getOAuthClient();
        const gmail = google.gmail({ version: "v1", auth });

        const res = await gmail.users.messages.list({
            userId: "me",
            labelIds: ["INBOX"],
            q: "is:unread",
            maxResults: maxResults,
        });

        if (!res.data.messages) {
            return [];
        }

        const emails = [];
        for (const msg of res.data.messages) {
            const emailData = await gmail.users.messages.get({
                userId: "me",
                id: msg.id,
                format: 'raw',
                metadataHeaders: ['From', 'To', 'Cc', 'Bcc', 'Subject', 'Date']
            });

            // 1. Check emailData.data type
            if (typeof emailData.data !== 'string') {
                console.error("Error: emailData.data is not a string!", typeof emailData.data);
                continue; // Skip this email and move to the next
            }

            try {
                const parsedEmail = await simpleParser(emailData.data);
                const emailDetails = (await gmail.users.messages.get({ userId: 'me', id: msg.id, format: 'metadata' })).data;

                // 1. email_metadata
                const sender = parsedEmail.from && parsedEmail.from.length > 0 ? {
                    name: parsedEmail.from[0].name || 'N/A',
                    email_address: parsedEmail.from[0].address || 'N/A',
                    importance_level: "unknown",
                    interaction_frequency: "new",
                    average_response_time_user: "new"
                } : { name: 'N/A', email_address: 'N/A', importance_level: "unknown", interaction_frequency: "new", average_response_time_user: "new" };

                const recipients = [];
                if (parsedEmail.to) {
                    recipients.push(...parsedEmail.to.map(recipient => ({
                        name: recipient.name || 'N/A',
                        email_address: recipient.address || 'N/A',
                        is_cc: false
                    })));
                }
                if (parsedEmail.cc) {
                    recipients.push(...parsedEmail.cc.map(recipient => ({
                        name: recipient.name || 'N/A',
                        email_address: recipient.address || 'N/A',
                        is_cc: true
                    })));
                }
                if (parsedEmail.bcc) {
                    recipients.push(...parsedEmail.bcc.map(recipient => ({
                        name: recipient.name || 'N/A',
                        email_address: recipient.address || 'N/A',
                        is_cc: true // Assuming BCC is also a form of CC in our structure
                    })));
                }

                const dateSent = parsedEmail.date ? parsedEmail.date.toISOString() : 'N/A';
                const hasAttachment = parsedEmail.attachments && parsedEmail.attachments.length > 0;
                const threadId = emailDetails.threadId || 'N/A';

                const emailMetadata = {
                    sender: sender,
                    recipients: recipients,
                    subject: parsedEmail.subject || 'No Subject',
                    date_sent: dateSent,
                    has_attachment: hasAttachment,
                    thread_id: threadId
                };

                // 2. nlp_analysis (placeholders for now)
                const nlpAnalysis = {
                    sentiment: { label: "neutral", confidence: 0.5 },
                    intent: { label: "unknown", confidence: 0.5 },
                    keywords: [],
                    urgency_indicators: [],
                    entities: {}
                };

                // 3. agent_analysis (placeholders for now)
                const agentAnalysis = {};

                // 4. user_context (placeholders for now, will populate later)
                const userContext = {
                    current_time: new Date().toISOString(),
                    current_location: "Winter Haven, Florida, United States",
                    user_preferences: {},
                    past_interactions_summary: {}
                };

                // 5. importance_scoring_instructions (already defined, placeholder)
                const importanceScoringInstructions = {
                    high_priority_indicators: [],
                    medium_priority_indicators: [],
                    weighting_factors: {},
                    scoring_logic_description: ""
                };

                emails.push({
                    email_metadata: emailMetadata,
                    nlp_analysis: nlpAnalysis,
                    agent_analysis: agentAnalysis,
                    user_context: userContext,
                    importance_scoring_instructions: importanceScoringInstructions
                });

            } catch (parseError) {
                console.error("Error parsing email:", parseError);
                continue; // Skip to the next email
            }

        }

        return emails;
    } catch (error) {
        console.error("Error fetching and parsing emails:", error);
        return [];
    }
}

// Helper function
function getHeader(headers, name) {
    const header = headers.find((h) => h.name.toLowerCase() === name.toLowerCase());
    return header ? header.value : "N/A";
}

// Example usage
async function main() {
    try {
        const emails = await getUnreadEmails(1);
        if (emails.length > 0) {
            console.log(JSON.stringify(emails, null, 2));
        } else {
            console.log("No unread emails found.");
        }
    } catch (error) {
        console.error("Error in main:", error);
    }
}

main();