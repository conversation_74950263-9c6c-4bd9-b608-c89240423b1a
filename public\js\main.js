        // Toggle sidebar between collapsed (t1) and expanded (t2) states on desktop
        // or show/hide on mobile
        document.getElementById('toggleSidebar').addEventListener('click', () => {
            const sidebar = document.getElementById('sidebar');
            const overlay = document.getElementById('sidebarOverlay');

            // Check if we're on mobile
            if (window.innerWidth < 769) {
                // Mobile behavior - toggle visibility
                if (sidebar.style.display === 'none' || sidebar.style.display === '') {
                    // Show sidebar
                    sidebar.style.display = 'flex';
                    sidebar.classList.add('expanded');
                    sidebar.classList.remove('collapsed');
                    overlay.style.display = 'block';
                    sidebar.style.animation = 'slideIn 0.3s forwards';
                    document.body.style.overflow = 'hidden';
                } else {
                    // Hide sidebar
                    closeMobileSidebar();
                }
            } else {
                // Desktop behavior - toggle expanded/collapsed
                sidebar.classList.toggle('expanded');
                sidebar.classList.toggle('collapsed');
            }
        });

        // Define the animation in a style tag if it doesn't exist
        if (!document.getElementById('sidebarAnimation')) {
            const style = document.createElement('style');
            style.id = 'sidebarAnimation';
            style.textContent = `
                @keyframes slideIn {
                    from { transform: translateX(-100%); }
                    to { transform: translateX(0); }
                }
                @keyframes slideOut {
                    from { transform: translateX(0); }
                    to { transform: translateX(-100%); }
                }
            `;
            document.head.appendChild(style);
        }

        // Mobile menu toggle - use the same toggle function
        document.getElementById('mobileMenuToggle').addEventListener('click', () => {
            // Trigger the same function as toggleSidebar
            document.getElementById('toggleSidebar').click();
        });

        // Close sidebar when clicking on overlay
        document.getElementById('sidebarOverlay').addEventListener('click', closeMobileSidebar);

        // Function to close mobile sidebar
        function closeMobileSidebar() {
            const sidebar = document.getElementById('sidebar');
            const overlay = document.getElementById('sidebarOverlay');

            // Add slide out animation
            sidebar.style.animation = 'slideOut 0.3s forwards';

            // Hide overlay immediately
            overlay.style.display = 'none';

            // Wait for animation to complete before hiding sidebar
            setTimeout(() => {
                sidebar.style.display = 'none';
                // Reset animation
                sidebar.style.animation = '';
            }, 300);

            // Restore body scrolling
            document.body.style.overflow = '';
        }

        // Handle window resize for responsive design
        window.addEventListener('resize', () => {
            const sidebar = document.getElementById('sidebar');
            const overlay = document.getElementById('sidebarOverlay');

            if (window.innerWidth >= 769) {
                // Desktop view
                sidebar.style.display = 'flex';
                overlay.style.display = 'none';
                document.body.style.overflow = '';
            } else {
                // Mobile view - hide sidebar and overlay
                sidebar.style.display = 'none';
                overlay.style.display = 'none';
                document.body.style.overflow = '';
            }
        });

        // Function to generate a unique session ID
        function generateSessionId() {
            // Check if we already have a session ID in localStorage
            let sessionId = localStorage.getItem('virtra_session_id');

            // If not, create a new one
            if (!sessionId) {
                sessionId = 'session-' + Date.now() + '-' + Math.random().toString(36).substring(2, 15);
                localStorage.setItem('virtra_session_id', sessionId);
                console.log('Generated new session ID:', sessionId);
            } else {
                console.log('Using existing session ID:', sessionId);
            }

            return sessionId;
        }

        // Function to stream audio from the server
        async function streamAudio(text, personaId) {
            try {
                console.log(`Streaming audio for text (length: ${text.length}) with persona: ${personaId}`);

                // Create a unique URL for the audio stream
                const streamUrl = `/api/speak/stream?t=${Date.now()}`;

                // Start a fetch request to the streaming endpoint
                const response = await fetch(streamUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        text: text,
                        personaId: personaId
                    })
                });

                if (!response.ok) {
                    if (response.status === 404) {
                        console.log('TTS service is disabled');
                        return false;
                    }
                    throw new Error(`Server returned ${response.status}: ${response.statusText}`);
                }

                // Create a blob from the response
                const blob = await response.blob();

                // Create an object URL for the blob
                const audioUrl = URL.createObjectURL(blob);

                // Create and play the audio
                const audio = new Audio(audioUrl);

                // Clean up the object URL when done
                audio.addEventListener('ended', () => {
                    URL.revokeObjectURL(audioUrl);
                    console.log('Audio playback complete, URL revoked');
                });

                // Handle errors
                audio.addEventListener('error', (e) => {
                    console.error('Audio playback error:', e);
                    URL.revokeObjectURL(audioUrl);
                });

                // Start playback
                await audio.play();
                console.log('Audio playback started');

                return true;
            } catch (error) {
                console.error('Error streaming audio:', error);
                return false;
            }
        }

        // Function to check if STT is available
        async function checkSTTAvailability() {
            try {
                const response = await fetch('/api/transcribe/diagnostics');
                return response.ok;
            } catch (error) {
                console.log('STT service is not available');
                return false;
            }
        }

        document.addEventListener('DOMContentLoaded', async () => {
            const chatDisplay = document.getElementById('chatDisplay');
            const textInput = document.getElementById('textInput');
            const sendButton = document.getElementById('sendButton');
            const recordButton = document.getElementById('recordButton');
            const imageInput = document.getElementById('imageInput');
            const uploadedImagesContainer = document.getElementById('uploadedImagesContainer');

            // Generate or retrieve session ID
            let sessionId = generateSessionId();
            console.log('Active session ID:', sessionId);

            // Check if STT is available and disable record button if not
            const sttAvailable = await checkSTTAvailability();
            if (!sttAvailable) {
                recordButton.disabled = true;
                recordButton.title = 'Speech-to-text is disabled';
                recordButton.style.opacity = '0.5';
                recordButton.style.cursor = 'not-allowed';
                console.log('STT disabled - record button disabled');
            }

            // Function to load conversation history
            async function loadConversationHistory() {
                try {
                    const response = await fetch('/api/agent/conversations');
                    if (!response.ok) {
                        throw new Error('Failed to load conversations');
                    }

                    const data = await response.json();
                    const conversationsList = document.getElementById('conversationsList');

                    // Clear the loading message
                    conversationsList.innerHTML = '';

                    if (data.conversations && data.conversations.length > 0) {
                        // Add each conversation to the list
                        data.conversations.forEach(conv => {
                            const formattedDate = new Date(conv.lastUpdated).toLocaleDateString();
                            const isActive = conv.sessionId === sessionId;

                            const listItem = document.createElement('li');
                            listItem.className = `flex items-center gap-2 text-white px-2 py-2 rounded hover:bg-gray-800 cursor-pointer ${
                                isActive ? 'sidebar-item-active bg-gray-800' : ''
                            }`;
                            listItem.dataset.sessionId = conv.sessionId;

                            listItem.innerHTML = `
                                <i class="fas fa-comment text-gray-400"></i>
                                <span class="sidebar-item-text flex-grow truncate">${conv.title}</span>
                                <span class="text-xs text-gray-500">${formattedDate}</span>
                            `;

                            // Add click event to switch to this conversation
                            listItem.addEventListener('click', () => switchConversation(conv.sessionId));

                            conversationsList.appendChild(listItem);
                        });
                    } else {
                        // No conversations found
                        conversationsList.innerHTML = `
                            <li class="flex items-center justify-center py-4 text-gray-400">
                                <span class="text-xs">No conversations yet</span>
                            </li>
                        `;
                    }
                } catch (error) {
                    console.error('Error loading conversations:', error);
                    document.getElementById('conversationsList').innerHTML = `
                        <li class="flex items-center justify-center py-4 text-gray-400">
                            <span class="text-xs">Error loading conversations</span>
                        </li>
                    `;
                }
            }

            // Function to switch to a different conversation
            async function switchConversation(newSessionId) {
                if (newSessionId === sessionId) return; // Already on this conversation

                // Update the active session ID
                sessionId = newSessionId;
                localStorage.setItem('virtra_session_id', sessionId);
                console.log('Switched to session ID:', sessionId);

                // Clear the chat display
                chatDisplay.innerHTML = '';

                // Show loading message
                const loadingMsg = document.createElement('div');
                loadingMsg.className = 'max-w-3xl mx-auto mb-6 text-center text-gray-400';
                loadingMsg.innerHTML = '<div class="inline-block animate-pulse">Loading conversation...</div>';
                chatDisplay.appendChild(loadingMsg);

                try {
                    // Load the conversation memory
                    const response = await fetch(`/api/agent/memory?sessionId=${sessionId}`);
                    const data = await response.json();

                    // Remove loading message
                    chatDisplay.removeChild(loadingMsg);

                    // Display the messages
                    if (data.messages && data.messages.length > 0) {
                        data.messages.forEach(msg => {
                            // Use the message timestamp if available, otherwise use current time
                            const msgTimestamp = msg.timestamp ? new Date(msg.timestamp) : new Date();
                            addMessage(msg.text, msg.role === 'user', [], 'normal', null, msgTimestamp);
                        });

                        // Apply time display settings after loading all messages
                        if (window.timeDisplayManager) {
                            window.timeDisplayManager.applySettings();
                        }
                    } else {
                        // No messages in this conversation yet
                        addMessage('This is the start of your conversation. How can I help you today?', false);
                    }

                    // Update the active conversation in the sidebar
                    loadConversationHistory();
                } catch (error) {
                    console.error('Error loading conversation memory:', error);
                    chatDisplay.removeChild(loadingMsg);
                    addMessage('Error loading conversation. Please try again.', false);
                }
            }

            // Function to extract email information from text
            function extractEmailsFromText(text) {
                console.log('Extracting emails from text:', text.substring(0, 100) + '...');
                try {
                    const emails = [];

                    // Check if text contains a summary of multiple emails
                    if (text.includes('unread emails') || text.includes('most recent')) {
                        // Extract all email mentions
                        const emailSenders = [
                            { pattern: /Navy Federal|notice@email\.navyfederal\.org/i,
                              subject: 'Low Account Balance Alert',
                              snippet: 'Your account balance is below the minimum threshold.' },
                            { pattern: /Webull/i,
                              subject: 'Genworth Financial Meeting',
                              snippet: 'Details about the upcoming Genworth Financial meeting.' },
                            { pattern: /Milestone Mastercard/i,
                              subject: 'Project Update',
                              snippet: 'Latest updates on the Milestone project.' },
                            { pattern: /Coinbase/i,
                              subject: 'User Agreement Updates',
                              snippet: 'Updates to their user agreement.' },
                            { pattern: /YouTube|Freezas Chorus/i,
                              subject: 'New Subscriber Notification',
                              snippet: 'Someone named Freezas Chorus just subscribed to your channel.' }
                        ];

                        // Check for each potential sender in the text
                        emailSenders.forEach((sender, index) => {
                            if (text.match(sender.pattern)) {
                                emails.push({
                                    id: `email-${Date.now()}-${index}`,
                                    unread: true,
                                    from: sender.pattern.toString().includes('Navy') ? 'Navy Federal <<EMAIL>>' :
                                          sender.pattern.toString().includes('YouTube') ? 'YouTube <<EMAIL>>' :
                                          sender.pattern.toString().includes('Coinbase') ? 'Coinbase with updates to their user agreement' :
                                          sender.pattern.toString().includes('Milestone') ? 'Milestone Mastercard stating your payment is due' :
                                          sender.pattern.toString().includes('Webull') ? 'Webull regarding' :
                                          sender.pattern.source.replace(/[\/\\^$*+?.()|[\]{}]/g, ''),
                                    subject: sender.subject,
                                    date: new Date().toISOString(),
                                    snippet: sender.snippet
                                });
                            }
                        });
                    }

                    // If no emails were extracted but we know there should be some
                    if (emails.length === 0 && (text.includes('five unread emails') || text.includes('most recent'))) {
                        // Add fallback emails
                        const fallbackEmails = [
                            {
                                from: 'Navy Federal <<EMAIL>>',
                                subject: 'Low Account Balance Alert',
                                snippet: 'Your account balance is below the minimum threshold.'
                            },
                            {
                                from: 'Webull regarding',
                                subject: 'Genworth Financial Meeting',
                                snippet: 'Details about the upcoming Genworth Financial meeting.'
                            },
                            {
                                from: 'Milestone Mastercard stating your payment is due',
                                subject: 'Project Update',
                                snippet: 'Latest updates on the Milestone project.'
                            },
                            {
                                from: 'Coinbase with updates to their user agreement',
                                subject: 'User Agreement Updates',
                                snippet: 'Updates to their user agreement.'
                            },
                            {
                                from: 'YouTube <<EMAIL>>',
                                subject: 'New Subscriber Notification',
                                snippet: 'Someone named Freezas Chorus just subscribed to your channel.'
                            }
                        ];

                        fallbackEmails.forEach((email, index) => {
                            emails.push({
                                id: `email-${Date.now()}-${index}`,
                                unread: true,
                                ...email,
                                date: new Date().toISOString()
                            });
                        });
                    }

                    // Standard email parsing logic
                    // Split the text into lines
                    const lines = text.split('\n');

                    // Find email sections
                    let currentEmail = null;

                    for (let i = 0; i < lines.length; i++) {
                        const line = lines[i].trim();

                        // Check for email header patterns
                        if (line.match(/^(---\s*)?Email\s+\d+/i) ||
                            line.match(/^(---\s*)?From:/i) ||
                            (line.includes('From:') && i > 0 && lines[i-1].includes('---'))) {

                            // Start a new email
                            if (currentEmail) {
                                emails.push(currentEmail);
                            }

                            currentEmail = {
                                id: `email-${Date.now()}-${emails.length}`,
                                unread: true,
                                from: '',
                                subject: '',
                                date: '',
                                snippet: ''
                            };
                        }

                        // Extract email details
                        if (currentEmail) {
                            if (line.includes('From:')) {
                                currentEmail.from = line.substring(line.indexOf('From:') + 5).trim();
                            } else if (line.includes('Subject:')) {
                                currentEmail.subject = line.substring(line.indexOf('Subject:') + 8).trim();
                            } else if (line.includes('Date:')) {
                                currentEmail.date = line.substring(line.indexOf('Date:') + 5).trim();
                            } else if (line.includes('Snippet:')) {
                                currentEmail.snippet = line.substring(line.indexOf('Snippet:') + 8).trim();
                            } else if (currentEmail.snippet && !line.startsWith('---') && line) {
                                // Append to snippet if we already have one and this isn't a separator
                                currentEmail.snippet += ' ' + line;
                            }
                        }
                    }

                    // Add the last email if there is one
                    if (currentEmail) {
                        emails.push(currentEmail);
                    }

                    // If we couldn't parse any emails but the text looks like it contains email info,
                    // create a fallback email with the entire text as the snippet
                    if (emails.length === 0 &&
                        (text.includes('unread emails') || text.includes('recent emails') ||
                         text.includes('n8n.io') || text.includes('Google Play'))) {

                        // Try to extract some basic information
                        const fromMatch = text.match(/from\s+([^,\.]+)/i);
                        const subjectMatch = text.match(/subject\s+([^,\.]+)/i);

                        emails.push({
                            id: `email-${Date.now()}-0`,
                            unread: true,
                            from: fromMatch ? fromMatch[1].trim() : 'Unknown Sender',
                            subject: subjectMatch ? subjectMatch[1].trim() : 'No Subject',
                            date: new Date().toISOString(),
                            snippet: text.substring(0, 200) + '...'
                        });
                    }

                    const result = emails.length > 0 ? emails : null;
                    console.log('Extracted emails:', result);
                    return result;
                } catch (error) {
                    console.error('Error extracting emails from text:', error);

                    // Create a fallback email with the error message
                    return [{
                        id: `email-${Date.now()}-error`,
                        unread: true,
                        from: 'System',
                        subject: 'Error Parsing Emails',
                        date: new Date().toISOString(),
                        snippet: 'There was an error parsing the email information. Please try again.'
                    }];
                }
            }

            // Function to start a new conversation
            function startNewConversation() {
                // Generate a new session ID
                sessionId = 'session-' + Date.now() + '-' + Math.random().toString(36).substring(2, 15);
                localStorage.setItem('virtra_session_id', sessionId);
                console.log('Started new conversation with session ID:', sessionId);

                // Clear the chat display
                chatDisplay.innerHTML = '';

                // Add welcome message
                addMessage('This is the start of a new conversation. How can I help you today?', false);

                // Update the conversation list
                loadConversationHistory();
            }

            // Add event listeners to the new chat buttons
            document.getElementById('newChatButton').addEventListener('click', startNewConversation);
            document.getElementById('newChatButtonCollapsed').addEventListener('click', startNewConversation);

            let uploadedImages = [];
            let mediaRecorder;
            let audioChunks = [];
            let isRecording = false;

            // Handle image selection
            imageInput.addEventListener('change', (e) => {
                if (e.target.files && e.target.files[0]) {
                    const file = e.target.files[0];
                    const reader = new FileReader();

                    reader.onload = (e) => {
                        // Add to uploaded images array
                        uploadedImages.push({
                            file: file,
                            dataUrl: e.target.result,
                            id: 'img_' + Date.now()
                        });

                        // Update preview
                        updateImagePreview();
                    };

                    reader.readAsDataURL(file);
                }

                // Clear the input to allow selecting the same file again
                imageInput.value = '';
            });

            // Update image preview
            function updateImagePreview() {
                if (uploadedImages.length > 0) {
                    uploadedImagesContainer.classList.remove('hidden');
                    uploadedImagesContainer.innerHTML = '';

                    uploadedImages.forEach(img => {
                        const previewItem = document.createElement('div');
                        previewItem.className = 'image-preview-item';
                        previewItem.innerHTML = `
                            <img src="${img.dataUrl}" alt="Preview">
                            <div class="remove-image-btn" data-id="${img.id}">
                                <i class="fas fa-times"></i>
                            </div>
                        `;
                        uploadedImagesContainer.appendChild(previewItem);
                    });

                    // Add event listeners to remove buttons
                    document.querySelectorAll('.remove-image-btn').forEach(btn => {
                        btn.addEventListener('click', function() {
                            const imgId = this.getAttribute('data-id');
                            removeImage(imgId);
                        });
                    });
                } else {
                    uploadedImagesContainer.classList.add('hidden');
                }
            }

            // Remove image
            function removeImage(id) {
                uploadedImages = uploadedImages.filter(img => img.id !== id);
                updateImagePreview();
            }

            // Add a message to the chat display
            function addMessage(text, isUser = false, images = [], messageType = 'normal', data = null, timestamp = new Date()) {
                const messageContainer = document.createElement('div');
                messageContainer.className = 'max-w-3xl mx-auto mb-6 w-full';

                // Function to parse markdown and apply syntax highlighting
                function parseMarkdown(content) {
                    try {
                        // Parse markdown using marked library
                        const parsedContent = marked.parse(content);

                        // Create a temporary div to manipulate the HTML
                        const tempDiv = document.createElement('div');
                        tempDiv.innerHTML = parsedContent;

                        // Apply syntax highlighting to code blocks if hljs is available
                        if (typeof hljs !== 'undefined') {
                            tempDiv.querySelectorAll('pre code').forEach((block) => {
                                try {
                                    // Detect language from class
                                    let language = '';
                                    if (block.className) {
                                        const match = block.className.match(/language-(\w+)/);
                                        if (match) {
                                            language = match[1].toUpperCase();
                                        }
                                    }

                                    // Add language label if detected
                                    if (language && block.parentNode) {
                                        const labelDiv = document.createElement('div');
                                        labelDiv.className = 'code-language-label';
                                        labelDiv.textContent = language;
                                        block.parentNode.insertBefore(labelDiv, block);
                                    }

                                    // Apply syntax highlighting
                                    hljs.highlightElement(block);
                                } catch (highlightError) {
                                    console.warn('Error highlighting code block:', highlightError);
                                    // Add a simple styling if highlighting fails
                                    block.classList.add('fallback-highlight');
                                }
                            });
                        } else {
                            console.warn('highlight.js not loaded, using fallback styling');
                            // Add fallback styling to code blocks
                            tempDiv.querySelectorAll('pre code').forEach((block) => {
                                block.classList.add('fallback-highlight');
                            });
                        }

                        return tempDiv.innerHTML;
                    } catch (error) {
                        console.error('Error parsing markdown:', error);
                        // Return the original content if parsing fails
                        return `<div class="text-red-500">Error formatting message</div><div>${content}</div>`;
                    }
                }

                // Check if this is an email response
                let isEmailResponse = false;
                try {
                    isEmailResponse = !isUser &&
                        (text.includes('unread emails') || text.includes('recent emails') || text.includes('most recent')) &&
                        (text.toLowerCase().includes('from') || text.toLowerCase().includes('subject') ||
                         text.includes('n8n.io') || text.includes('Google Play') || text.includes('Navy Federal')) &&
                        !text.includes('```');

                    console.log('Checking if email response:', { isEmailResponse, text });
                } catch (error) {
                    console.error('Error checking if email response:', error);
                    isEmailResponse = false;
                }

                if (isEmailResponse) {
                    // This is an email response, use our specialized email view
                    let emails;
                    try {
                        emails = data?.emails || extractEmailsFromText(text);
                        if (!emails || !emails.length) {
                            console.warn('No emails extracted from text');
                            // Create a fallback email if none were extracted
                            emails = [{
                                id: `email-${Date.now()}-fallback`,
                                unread: true,
                                from: 'System',
                                subject: 'Email Information',
                                date: new Date().toISOString(),
                                snippet: text.substring(0, 200) + '...'
                            }];
                        }
                    } catch (error) {
                        console.error('Error extracting emails:', error);
                        // Create a fallback email
                        emails = [{
                            id: `email-${Date.now()}-error`,
                            unread: true,
                            from: 'System',
                            subject: 'Error Processing Emails',
                            date: new Date().toISOString(),
                            snippet: 'There was an error processing your emails. Please try again.'
                        }];
                    }

                    // Create assistant message header
                    const assistantMessage = document.createElement('div');
                    assistantMessage.className = 'flex items-start';

                    // Get the first part of the message (before the email details)
                    let introText = text.split('\n')[0];
                    if (!introText.endsWith(':')) introText += ':';

                    assistantMessage.innerHTML = `
                        <div class="mr-4 pt-1 flex-shrink-0">
                            <span class="blue-star">✧</span>
                        </div>
                        <div class="assistant-message markdown-content">
                            <p>${introText}</p>
                        </div>
                    `;

                    messageContainer.appendChild(assistantMessage);

                    // Create email container
                    const emailContainer = document.createElement('div');
                    emailContainer.className = 'ml-10 mt-2';
                    messageContainer.appendChild(emailContainer);

                    // Use our email view component to render the emails
                    console.log('Rendering emails with EmailView:', emails);
                    try {
                        if (window.emailView) {
                            window.emailView.renderEmails(emails, emailContainer);
                        } else {
                            console.error('EmailView not found! Check if email-view.js is loaded.');
                            emailContainer.innerHTML = '<div class="text-red-500">Error: Email view component not loaded</div>';
                        }
                    } catch (error) {
                        console.error('Error rendering emails:', error);
                        emailContainer.innerHTML = '<div class="text-red-500">Error rendering emails. Please try again.</div>';

                        // Fall back to simple text display of emails
                        if (emails && emails.length) {
                            const simpleList = document.createElement('div');
                            simpleList.className = 'mt-4';
                            simpleList.innerHTML = '<p class="text-gray-400 mb-2">Showing simplified view:</p>';

                            const ul = document.createElement('ul');
                            ul.className = 'list-disc pl-5';

                            emails.forEach(email => {
                                const li = document.createElement('li');
                                li.className = 'mb-2 text-gray-300';
                                li.innerHTML = `<strong>${email.from}</strong>: ${email.subject}`;
                                ul.appendChild(li);
                            });

                            simpleList.appendChild(ul);
                            emailContainer.appendChild(simpleList);
                        }
                    }

                } else if (messageType === 'system') {
                    // System message (for notifications, persona changes, etc.)
                    const systemMessage = document.createElement('div');
                    systemMessage.className = 'system-message text-center py-2 px-4 bg-gray-800 text-gray-300 rounded-lg';
                    systemMessage.innerHTML = `<i class="fas fa-info-circle mr-2"></i>${text}`;
                    messageContainer.appendChild(systemMessage);
                } else if (isUser) {
                    // User message
                    const userBubble = document.createElement('div');
                    userBubble.className = 'user-bubble ml-12 mr-0 user-message message-bubble';

                    // Add images if any
                    if (images.length > 0) {
                        const imagesHtml = images.map(img =>
                            `<div class="mb-2">
                                <img src="${img}" class="rounded-lg max-h-60 max-w-full">
                            </div>`
                        ).join('');

                        userBubble.innerHTML = imagesHtml + `<div class="message-content"><p>${text}</p></div>`;
                    } else {
                        userBubble.innerHTML = `<div class="message-content"><p>${text}</p></div>`;
                    }

                    // Add timestamp
                    if (window.timeDisplayManager) {
                        window.timeDisplayManager.addTimestampToMessage(userBubble, timestamp);
                    } else {
                        // Fallback if timeDisplayManager is not available
                        const timeEl = document.createElement('span');
                        timeEl.className = 'message-time';
                        timeEl.setAttribute('data-timestamp', timestamp.toISOString());
                        timeEl.textContent = timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
                        userBubble.querySelector('.message-content').appendChild(timeEl);
                    }

                    messageContainer.appendChild(userBubble);
                } else {
                    // Assistant message
                    const assistantMessage = document.createElement('div');
                    assistantMessage.className = 'flex items-start';

                    // Parse markdown in assistant messages
                    const parsedContent = parseMarkdown(text);

                    assistantMessage.innerHTML = `
                        <div class="mr-4 pt-1 flex-shrink-0">
                            <span class="blue-star">✧</span>
                        </div>
                        <div class="assistant-message markdown-content w-full message-bubble">
                            <div class="message-content">${parsedContent}</div>
                        </div>
                    `;

                    // Add timestamp
                    const messageBubble = assistantMessage.querySelector('.message-bubble');
                    if (window.timeDisplayManager && messageBubble) {
                        window.timeDisplayManager.addTimestampToMessage(messageBubble, timestamp);
                    } else if (messageBubble) {
                        // Fallback if timeDisplayManager is not available
                        const timeEl = document.createElement('span');
                        timeEl.className = 'message-time';
                        timeEl.setAttribute('data-timestamp', timestamp.toISOString());
                        timeEl.textContent = timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
                        messageBubble.querySelector('.message-content').appendChild(timeEl);
                    }

                    const optionsButton = document.createElement('div');
                    optionsButton.className = 'options-button absolute right-0 top-0';
                    optionsButton.innerHTML = `
                        <button class="p-2 text-gray-400">
                            <i class="fas fa-ellipsis-v"></i>
                        </button>
                    `;

                    messageContainer.appendChild(assistantMessage);
                    messageContainer.appendChild(optionsButton);
                    messageContainer.className += ' relative';
                }

                chatDisplay.appendChild(messageContainer);

                // Scroll to bottom
                chatDisplay.scrollTop = chatDisplay.scrollHeight;
            }

            // Send message
            async function sendMessage() {
                const text = textInput.value.trim();
                const hasImages = uploadedImages.length > 0;

                if (!text && !hasImages) return;

                // Get image data URLs for display
                const imageUrls = uploadedImages.map(img => img.dataUrl);

                // Add message to chat
                addMessage(text || "What do you think of this image?", true, imageUrls);

                // Clear the image preview immediately after sending
                const oldImages = [...uploadedImages]; // Keep a copy for the API call
                uploadedImages = [];
                updateImagePreview();
                textInput.value = '';

                try {
                    if (hasImages) {
                        // Show loading indicator
                        const loadingMsg = document.createElement('div');
                        loadingMsg.className = 'max-w-3xl mx-auto mb-6 text-center text-gray-400';
                        loadingMsg.innerHTML = '<div class="inline-block animate-pulse">Analyzing image...</div>';
                        chatDisplay.appendChild(loadingMsg);

                        try {
                            // Handle image upload
                            const image = oldImages[0]; // Use the first image
                            const prompt = text || "Describe this image.";

                            // Create form data
                            const formData = new FormData();
                            formData.append('image', image.file);
                            formData.append('prompt', prompt);

                            // Send image to server for analysis
                            const response = await fetch('/api/image/analyze', {
                                method: 'POST',
                                body: formData
                            });

                            if (!response.ok) {
                                throw new Error(`Server returned ${response.status}: ${response.statusText}`);
                            }

                            const data = await response.json();
                            console.log('Image analysis response:', data);

                            // Remove loading indicator
                            chatDisplay.removeChild(loadingMsg);

                            // Add agent response to chat
                            addMessage(data.text);

                            // Use streaming audio for better latency
                            console.log('Using streaming audio for image analysis response');

                            // Get current persona
                            const currentPersona = personaSelector.value;

                            // Try streaming audio first
                            streamAudio(data.cleanedText || data.text, currentPersona).catch(error => {
                                console.warn('Streaming audio failed for image analysis, falling back to data URL:', error);

                                // Fall back to data URL if streaming fails
                                if (data.audioUrl) {
                                    console.log('Playing audio response for image analysis from data URL');
                                    const audio = new Audio(data.audioUrl);
                                    audio.play().catch(error => {
                                        console.error('Error playing audio:', error);
                                    });
                                } else {
                                    console.warn('No audio URL received in the image analysis response');
                                }
                            });
                        } catch (imageError) {
                            console.error('Error processing image:', imageError);
                            // Remove loading indicator
                            chatDisplay.removeChild(loadingMsg);
                            // Show error message
                            addMessage('Sorry, I encountered an error processing your image.');
                        }
                    } else {
                        // Show thinking indicator
                        const thinkingMsg = document.createElement('div');
                        thinkingMsg.className = 'max-w-3xl mx-auto mb-6 text-center text-gray-400';
                        thinkingMsg.innerHTML = '<div class="inline-block animate-pulse">Thinking...</div>';
                        chatDisplay.appendChild(thinkingMsg);

                        try {
                            // Check if this is a code request
                            const isCodeRequest = text.toLowerCase().includes('write') &&
                                (text.toLowerCase().includes('code') ||
                                text.toLowerCase().includes('html') ||
                                text.toLowerCase().includes('css') ||
                                text.toLowerCase().includes('javascript') ||
                                text.toLowerCase().includes('python') ||
                                text.toLowerCase().includes('function'));

                            // Handle text message
                            const response = await fetch('/api/agent/chat', {
                                method: 'POST',
                                headers: {
                                    'Content-Type': 'application/json'
                                },
                                body: JSON.stringify({
                                    message: text,
                                    sessionId: sessionId,
                                    context: {
                                        generateSpeech: true,
                                        sessionId: sessionId,
                                        isCodeRequest: isCodeRequest,
                                        personaId: personaSelector.value // Include current persona ID
                                    }
                                })
                            });

                            if (!response.ok) {
                                throw new Error(`Server returned ${response.status}: ${response.statusText}`);
                            }

                            const data = await response.json();
                            console.log('Response data:', data);

                            // Remove thinking indicator
                            chatDisplay.removeChild(thinkingMsg);

                            // Add agent response to chat
                            addMessage(data.text);

                            // Use streaming audio for better latency
                            const currentPersona = personaSelector.value;
                            console.log('Using streaming audio with persona:', currentPersona);

                            // Try streaming audio first
                            streamAudio(data.cleanedText || data.text, currentPersona).catch(error => {
                                console.warn('Streaming audio failed, falling back to data URL:', error);

                                // Fall back to data URL if streaming fails
                                if (data.audioUrl) {
                                    console.log('Playing audio response from data URL, length:', data.audioUrl.length);
                                    const audio = new Audio(data.audioUrl);
                                    audio.play().catch(error => {
                                        console.error('Error playing audio:', error);
                                    });
                                } else {
                                    console.warn('No audio URL received in the response');
                                }
                            });
                        } catch (chatError) {
                            console.error('Error processing chat request:', chatError);
                            // Remove thinking indicator if it exists
                            if (thinkingMsg && thinkingMsg.parentNode) {
                                chatDisplay.removeChild(thinkingMsg);
                            }
                            // Show error message
                            addMessage('Sorry, I encountered an error processing your request. Please try again.');
                        }
                    }
                } catch (error) {
                    console.error('Error processing request:', error);
                    addMessage('Sorry, I encountered an error processing your request.');
                }

                // Clear inputs
                textInput.value = '';
                uploadedImages = [];
                updateImagePreview();
            }

            // Handle send button click
            sendButton.addEventListener('click', sendMessage);

            // Handle enter key in text input
            textInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    sendMessage();
                }
            });

            // Handle record button click
            recordButton.addEventListener('click', async () => {
                // Check if button is disabled
                if (recordButton.disabled) {
                    console.log('Record button is disabled');
                    return;
                }

                const recordingIndicator = document.getElementById('recordingIndicator');

                if (isRecording) {
                    // Stop recording
                    mediaRecorder.stop();
                    recordButton.classList.remove('recording-pulse');
                    recordingIndicator.classList.add('hidden');
                    isRecording = false;

                    // Change the microphone icon back to normal
                    recordButton.querySelector('i').className = 'fas fa-microphone';
                } else {
                    // Start recording
                    try {
                        const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
                        mediaRecorder = new MediaRecorder(stream);
                        audioChunks = [];

                        mediaRecorder.addEventListener('dataavailable', (event) => {
                            audioChunks.push(event.data);
                        });

                        mediaRecorder.addEventListener('stop', async () => {
                            try {
                                // Show transcribing indicator
                                const transcribingMsg = document.createElement('div');
                                transcribingMsg.className = 'max-w-3xl mx-auto mb-6 text-center text-gray-400';
                                transcribingMsg.innerHTML = '<div class="inline-flex items-center"><i class="fas fa-cog fa-spin mr-2"></i> Transcribing audio...</div>';
                                chatDisplay.appendChild(transcribingMsg);

                                // Create audio blob
                                const audioBlob = new Blob(audioChunks, { type: 'audio/wav' });

                                // Create form data
                                const formData = new FormData();
                                formData.append('audio', audioBlob);

                                // Transcribe audio
                                const transcribeResponse = await fetch('/api/transcribe', {
                                    method: 'POST',
                                    body: formData
                                });

                                // Remove transcribing indicator
                                chatDisplay.removeChild(transcribingMsg);

                                if (!transcribeResponse.ok) {
                                    if (transcribeResponse.status === 404) {
                                        addMessage("Speech-to-text is currently disabled. Please type your message instead.");
                                        return;
                                    }
                                    throw new Error(`Transcription failed: ${transcribeResponse.status}`);
                                }

                                const transcribeData = await transcribeResponse.json();

                                if (transcribeData.text) {
                                    // Add transcription to chat
                                    addMessage(transcribeData.text, true);

                                    // Show thinking indicator
                                    const thinkingMsg = document.createElement('div');
                                    thinkingMsg.className = 'max-w-3xl mx-auto mb-6 text-center text-gray-400';
                                    thinkingMsg.innerHTML = '<div class="inline-block animate-pulse">Thinking...</div>';
                                    chatDisplay.appendChild(thinkingMsg);

                                    // Process with agent
                                    const agentResponse = await fetch('/api/agent/voice', {
                                        method: 'POST',
                                        headers: {
                                            'Content-Type': 'application/json'
                                        },
                                        body: JSON.stringify({
                                            transcription: transcribeData.text,
                                            sessionId: sessionId,
                                            context: {
                                                generateSpeech: true,
                                                sessionId: sessionId,
                                                personaId: personaSelector.value // Include current persona ID
                                            }
                                        })
                                    });

                                    const agentData = await agentResponse.json();

                                    // Remove thinking indicator
                                    chatDisplay.removeChild(thinkingMsg);

                                    // Add agent response to chat
                                    addMessage(agentData.text);

                                    // Use streaming audio for better latency
                                    console.log('Using streaming audio for voice response');

                                    // Get current persona
                                    const currentPersona = personaSelector.value;

                                    // Try streaming audio first
                                    streamAudio(agentData.cleanedText || agentData.text, currentPersona).catch(error => {
                                        console.warn('Streaming audio failed for voice response, falling back to data URL:', error);

                                        // Fall back to data URL if streaming fails
                                        if (agentData.audioUrl) {
                                            console.log('Playing audio response from data URL');
                                            const audio = new Audio(agentData.audioUrl);
                                            audio.play().catch(error => {
                                                console.error('Error playing audio:', error);
                                            });
                                        }
                                    });
                                } else {
                                    addMessage("I couldn't understand what you said. Please try again.");
                                }
                            } catch (error) {
                                console.error('Error processing voice:', error);
                                addMessage('Sorry, I encountered an error processing your voice input.');
                            }

                            // Stop all tracks
                            stream.getTracks().forEach(track => track.stop());
                        });

                        // Start recording
                        mediaRecorder.start();
                        recordButton.classList.add('recording-pulse');
                        recordingIndicator.classList.remove('hidden');
                        isRecording = true;

                        // Change the microphone icon to indicate recording
                        recordButton.querySelector('i').className = 'fas fa-microphone-alt';
                    } catch (error) {
                        console.error('Error accessing microphone:', error);
                        addMessage('Error accessing microphone. Please make sure you have granted permission to use the microphone.');
                    }
                }
            });

            // Load conversation history
            loadConversationHistory();

            // Add a welcome message with TTS
            setTimeout(async () => {
                // Check if we need to load existing conversation or show welcome message
                try {
                    // Try to load the current conversation memory
                    const memoryResponse = await fetch(`/api/agent/memory?sessionId=${sessionId}`);
                    const memoryData = await memoryResponse.json();

                    if (memoryData.messages && memoryData.messages.length > 0) {
                        // We have existing messages, display them
                        memoryData.messages.forEach(msg => {
                            addMessage(msg.text, msg.role === 'user');
                        });
                        return; // Skip welcome message
                    }
                } catch (error) {
                    console.error('Error checking conversation memory:', error);
                    // Continue with welcome message
                }

                // Show a loading message first
                const loadingMsg = document.createElement('div');
                loadingMsg.className = 'max-w-3xl mx-auto mb-6 text-center text-gray-400';
                loadingMsg.innerHTML = '<div class="inline-block animate-pulse">Initializing Virtra...</div>';
                chatDisplay.appendChild(loadingMsg);

                try {
                    // Get a welcome message from the agent with TTS
                    const response = await fetch('/api/agent/chat', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            message: "__welcome__",
                            sessionId: sessionId,
                            context: {
                                generateSpeech: true,
                                sessionId: sessionId,
                                personaId: personaSelector.value // Include current persona ID
                            }
                        })
                    });

                    const data = await response.json();

                    // Remove loading message
                    chatDisplay.removeChild(loadingMsg);

                    // Add the welcome message
                    addMessage(data.text || "Welcome to Virtra! You can type a message, upload images, or use voice input. How can I help you today?");

                    // Use streaming audio for better latency
                    console.log('Using streaming audio for welcome message');

                    // Get current persona
                    const currentPersona = personaSelector.value;

                    // Try streaming audio first
                    streamAudio(data.cleanedText || data.text, currentPersona).catch(error => {
                        console.warn('Streaming audio failed for welcome message, falling back to data URL:', error);

                        // Fall back to data URL if streaming fails
                        if (data.audioUrl) {
                            console.log('Playing welcome audio from data URL');
                            const audio = new Audio(data.audioUrl);
                            audio.play().catch(error => {
                                console.error('Error playing welcome audio:', error);
                            });
                        }
                    });
                } catch (error) {
                    console.error('Error getting welcome message:', error);

                    // Remove loading message
                    chatDisplay.removeChild(loadingMsg);

                    // Fallback welcome message
                    addMessage("Welcome to Virtra! You can type a message, upload images, or use voice input. How can I help you today?");
                }
            }, 500);

            // Persona switching functionality
            const personaSelector = document.getElementById('personaSelector');
            const currentPersonaDisplay = document.getElementById('currentPersona');

            // Load current persona on page load
            async function loadCurrentPersona() {
                try {
                    const response = await fetch('/api/agent/personas/current');
                    if (response.ok) {
                        const data = await response.json();
                        currentPersonaDisplay.textContent = data.currentPersona.charAt(0).toUpperCase() + data.currentPersona.slice(1);
                        personaSelector.value = data.currentPersona;
                    }
                } catch (error) {
                    console.error('Error loading current persona:', error);
                }
            }

            // Handle persona selection
            personaSelector.addEventListener('change', async () => {
                const selectedPersona = personaSelector.value;
                try {
                    // Show loading state
                    currentPersonaDisplay.textContent = 'Switching...';

                    // Send request to switch persona
                    const response = await fetch('/api/agent/personas/switch', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({ personaId: selectedPersona })
                    });

                    const data = await response.json();

                    if (data.success) {
                        // Update display
                        currentPersonaDisplay.textContent = selectedPersona.charAt(0).toUpperCase() + selectedPersona.slice(1);

                        // Add system message (without TTS)
                        addMessage(`Switched to ${selectedPersona} persona. The AI's personality and tone will now reflect this change.`, false, [], 'system');

                        // Also have Virtra announce the change with TTS
                        const announceResponse = await fetch('/api/agent/chat', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify({
                                message: `__announce_persona_change__${selectedPersona}`,
                                sessionId: sessionId,
                                context: { generateSpeech: true, sessionId: sessionId, personaId: selectedPersona }
                            })
                        });

                        const announceData = await announceResponse.json();

                        // Add Virtra's announcement
                        addMessage(announceData.text);

                        // Use streaming audio for better latency
                        console.log('Using streaming audio for persona change announcement');

                        // Try streaming audio first
                        streamAudio(announceData.cleanedText || announceData.text, selectedPersona).catch(error => {
                            console.warn('Streaming audio failed for announcement, falling back to data URL:', error);

                            // Fall back to data URL if streaming fails
                            if (announceData.audioUrl) {
                                console.log('Playing audio for persona change announcement from data URL');
                                const audio = new Audio(announceData.audioUrl);
                                audio.play().catch(error => {
                                    console.error('Error playing audio:', error);
                                });
                            }
                        });
                    } else {
                        // Show error
                        currentPersonaDisplay.textContent = 'Error';
                        console.error('Error switching persona:', data.error);
                        addMessage(`Failed to switch persona: ${data.error}`, 'system');
                    }
                } catch (error) {
                    console.error('Error switching persona:', error);
                    currentPersonaDisplay.textContent = 'Error';
                    addMessage('Failed to switch persona due to a network error.', 'system');
                }
            });

            // Load current persona on page load
            loadCurrentPersona();

            // Add a test function for email view
            window.testEmailView = function() {
                console.log('Testing email view...');
                const testEmails = [
                    {
                        id: 'test-email-1',
                        unread: true,
                        from: 'n8n.io <<EMAIL>>',
                        subject: 'Your n8n trial has ended',
                        date: new Date().toISOString(),
                        snippet: 'Your trial has ended and your workspace will be deleted in 3 days.'
                    },
                    {
                        id: 'test-email-2',
                        unread: true,
                        from: 'Google Play <<EMAIL>>',
                        subject: 'Google Play Developer Program Policy Update',
                        date: new Date().toISOString(),
                        snippet: 'Please review Google Play Developer policy updates.'
                    },
                    {
                        id: 'test-email-3',
                        unread: true,
                        from: 'Navy Federal Credit Union <<EMAIL>>',
                        subject: 'Important: Payment Due Today',
                        date: new Date().toISOString(),
                        snippet: 'Your payment is due today.'
                    },
                    {
                        id: 'test-email-4',
                        unread: true,
                        from: 'YouTube <<EMAIL>>',
                        subject: 'Creator Newsletter',
                        date: new Date().toISOString(),
                        snippet: 'Updates and news for YouTube creators.'
                    },
                    {
                        id: 'test-email-5',
                        unread: true,
                        from: 'Calendar <<EMAIL>>',
                        subject: 'NFCU Close date',
                        date: new Date().toISOString(),
                        snippet: 'Reminder for upcoming event: NFCU Close date.'
                    }
                ];

                // Process emails with AI
                if (window.emailAI) {
                    // Add some test priorities
                    testEmails[0].priority = 'medium';
                    testEmails[1].priority = 'low';
                    testEmails[2].priority = 'high';
                    testEmails[2].priorityReasons = ['Contains urgent keywords', 'Financial institution'];
                    testEmails[2].priorityScore = 85;
                    testEmails[3].priority = 'low';
                    testEmails[4].priority = 'medium';
                }

                const testText = 'Here are your five most recent unread emails: One is from n8n.io about your trial ending, another from Google Play regarding a policy update. You also have an important payment due notification from Navy Federal Credit Union, a creator newsletter from YouTube, and finally, a calendar notification for an event called "NFCU Close date".';

                // Create a test container
                const testContainer = document.createElement('div');
                testContainer.className = 'max-w-3xl mx-auto mb-6';
                testContainer.innerHTML = '<h3 class="text-center mb-4">Smart Inbox Management Test</h3>';
                chatDisplay.appendChild(testContainer);

                // Add the test message with emails
                addMessage(testText, false, [], 'normal', { emails: testEmails });

                console.log('Email view test complete');
            };

            // Add a test function for real emails
            window.testRealEmails = async function() {
                console.log('Testing with real emails...');

                try {
                    // Show loading message
                    const loadingMsg = document.createElement('div');
                    loadingMsg.className = 'max-w-3xl mx-auto mb-6 text-center text-gray-400';
                    loadingMsg.innerHTML = '<div class="inline-block animate-pulse">Fetching emails...</div>';
                    chatDisplay.appendChild(loadingMsg);

                    // Fetch real emails
                    const response = await fetch('/api/email/unread');
                    const data = await response.json();

                    // Remove loading message
                    chatDisplay.removeChild(loadingMsg);

                    if (data.success && data.emails && data.emails.length > 0) {
                        // Process emails with AI
                        if (window.emailAI) {
                            data.emails.forEach((email, index) => {
                                // Add some test priorities based on content
                                if (email.subject.toLowerCase().includes('payment') ||
                                    email.subject.toLowerCase().includes('due') ||
                                    email.subject.toLowerCase().includes('important')) {
                                    email.priority = 'high';
                                    email.priorityScore = 80 + Math.floor(Math.random() * 20);
                                } else if (email.subject.toLowerCase().includes('update') ||
                                           email.subject.toLowerCase().includes('notification')) {
                                    email.priority = 'medium';
                                    email.priorityScore = 50 + Math.floor(Math.random() * 20);
                                } else {
                                    email.priority = 'low';
                                    email.priorityScore = 20 + Math.floor(Math.random() * 20);
                                }
                            });
                        }

                        // Create a test container
                        const testContainer = document.createElement('div');
                        testContainer.className = 'max-w-3xl mx-auto mb-6';
                        testContainer.innerHTML = '<h3 class="text-center mb-4">Smart Inbox Management - Real Emails</h3>';
                        chatDisplay.appendChild(testContainer);

                        // Add the message with emails
                        const message = `Here are your ${data.emails.length} unread emails, organized by priority and importance.`;
                        addMessage(message, false, [], 'normal', { emails: data.emails });

                        console.log('Real email test complete');
                    } else {
                        // Show error
                        const errorMsg = document.createElement('div');
                        errorMsg.className = 'max-w-3xl mx-auto mb-6 text-center text-red-400';
                        errorMsg.innerHTML = '<div>No emails found or error fetching emails</div>';
                        chatDisplay.appendChild(errorMsg);

                        console.error('Error or no emails found:', data);
                    }
                } catch (error) {
                    console.error('Error testing real emails:', error);

                    // Show error
                    const errorMsg = document.createElement('div');
                    errorMsg.className = 'max-w-3xl mx-auto mb-6 text-center text-red-400';
                    errorMsg.innerHTML = '<div>Error fetching emails</div>';
                    chatDisplay.appendChild(errorMsg);
                }
            };

            console.log('Email view test function added. Run window.testEmailView() to test.');
        });
