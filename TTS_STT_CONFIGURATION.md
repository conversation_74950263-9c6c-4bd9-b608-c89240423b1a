# TTS and STT Configuration Guide

This guide explains how to enable or disable Text-to-Speech (TTS) and Speech-to-Text (STT) features to manage billing costs.

## Quick Setup to Disable TTS and STT

To disable both TTS and STT features (to avoid billing charges), add these lines to your `.env` file:

```env
# Feature flags to disable TTS and STT to avoid billing
ENABLE_TTS=false
ENABLE_STT=false
```

## Environment Variables

### ENABLE_TTS
- **Default**: `true` (enabled)
- **To disable**: Set to `false`
- **Effect**: Disables all text-to-speech functionality
  - `/api/speak` routes will not be available
  - Audio responses will not be generated
  - Frontend will gracefully handle missing TTS

### ENABLE_STT
- **Default**: `true` (enabled)
- **To disable**: Set to `false`
- **Effect**: Disables all speech-to-text functionality
  - `/api/transcribe` routes will not be available
  - Record button will be disabled in the UI
  - Voice input will not be available

## What Happens When Disabled

### When TTS is Disabled:
- The `/api/speak` and `/api/speak/stream` endpoints return 404
- The agent still responds with text, but no audio is generated
- Frontend gracefully handles missing audio and continues normally
- No Google Cloud TTS API calls are made (no billing)

### When STT is Disabled:
- The `/api/transcribe` endpoint returns 404
- The record button is disabled and grayed out in the UI
- Users can still type messages normally
- No Google Cloud STT API calls are made (no billing)

## Re-enabling Features

To re-enable either feature, simply change the value to `true` or remove the line entirely:

```env
# Re-enable TTS and STT
ENABLE_TTS=true
ENABLE_STT=true
```

Or remove the lines completely (features are enabled by default).

## Testing the Configuration

1. **Test TTS disabled**: Send a message and verify no audio plays
2. **Test STT disabled**: Check that the record button is grayed out and disabled
3. **Check console logs**: Look for messages like "TTS routes disabled" or "STT routes disabled"

## Current Status

As configured in your `.env` file, both TTS and STT are currently **DISABLED** to avoid billing charges.

The application will work normally for text-based conversations, but voice features will not be available.
