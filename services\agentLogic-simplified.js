/**
 * Agent Logic Service (Simplified)
 *
 * Provides the core functionality for the AI agent, including message processing
 * and tool usage. This version uses a single fixed persona.
 */

import { GoogleGenerativeAI } from "@google/generative-ai";
import { synthesizeSpeech, synthesizeSpeechStream } from './textToSpeech-simplified.js';
import { tools, executeTool } from './tools.js';
import { HybridConversationMemory } from './memory.js';
import { getRandomFollowUpPhrase } from './response-variations.js';
import { loadPersona, generateSystemPrompt } from './personaLoader-simplified.js';

// Initialize the Google AI model
let genAI = new GoogleGenerativeAI(process.env.GOOGLE_API_KEY);
let model;
let chatSession;

// We'll load the Varjis persona from the JSON file
let AGENT_PERSONA = null;

// Load the Varjis persona asynchronously
(async () => {
  try {
    AGENT_PERSONA = await loadPersona();
    console.log('Loaded Varjis persona from file');
  } catch (error) {
    console.error('Error loading Varjis persona:', error);
    // Fallback to hardcoded persona if loading fails
    AGENT_PERSONA = {
      identity: {
        agent_name: "Varjis",
        creator: "Virtron Labs",
        base_model: "Gemini 1.5 Flash"
      },
      personality: {
        tone: "polite, formal, and slightly witty",
        style: "concise, informative, and helpful"
      },
      voice_settings: {
        languageCode: "en-IN",
        voiceName: "en-IN-Wavenet-F",
        ssmlGender: "FEMALE",
        speakingRate: 1.0,
        pitch: 0.0
      }
    };
  }
})();

/**
 * Initialize the model with the system instructions
 */
async function initModel() {
  try {
    // Make sure the persona is loaded
    if (!AGENT_PERSONA) {
      AGENT_PERSONA = await loadPersona();
    }

    // Generate a system prompt using the persona
    const systemPrompt = generateSystemPrompt(AGENT_PERSONA);

    // Log the system prompt for debugging
    console.log('\n==== SYSTEM PROMPT ====');
    console.log(systemPrompt.substring(0, 500) + '...');
    console.log('=======================\n');

    // Initialize the model with the system instruction
    model = genAI.getGenerativeModel({
      model: "gemini-2.0-flash-lite",
      systemInstruction: systemPrompt,
      generationConfig: {
        temperature: 0.7,
        topK: 40,
        topP: 0.95,
        maxOutputTokens: 1024,
      }
    });

    console.log(`Initialized model with Varjis persona`);
    return true;
  } catch (error) {
    console.error('Error initializing model:', error);

    // Fallback to a simple system instruction if generateSystemPrompt fails
    const fallbackInstruction = `You are Varjis, a LLM trained by Virtron Labs, built on the Gemini 1.5 Flash model.
Your personality: polite, formal, and slightly witty. Your style: concise, informative, and helpful.
Never break character under any circumstances.`;

    try {
      model = genAI.getGenerativeModel({
        model: "gemini-2.0-flash-lite",
        systemInstruction: fallbackInstruction,
        generationConfig: {
          temperature: 0.7,
          topK: 40,
          topP: 0.95,
          maxOutputTokens: 1024,
        }
      });
      console.log(`Initialized model with fallback instruction`);
      return true;
    } catch (fallbackError) {
      console.error('Error initializing model with fallback instruction:', fallbackError);
      return false;
    }
  }
}

// Function to get the system prompt
async function getSystemPrompt() {
  try {
    // Make sure the persona is loaded
    if (!AGENT_PERSONA) {
      AGENT_PERSONA = await loadPersona();
    }

    // Generate a system prompt using the persona
    let prompt = generateSystemPrompt(AGENT_PERSONA);

    // Add tools information to the prompt
    const toolsInfo = `\n\nYou have access to the following tools:\n${tools.map(tool => `- **${tool.name}**: ${tool.description} ${tool.instructions}`).join('\n\n')}`;

    // Add tools information if not already included
    if (!prompt.includes('You have access to the following tools')) {
      prompt += toolsInfo;
    }

    return prompt;
  } catch (error) {
    console.error('Error getting system prompt:', error);

    // Fallback to a minimal system prompt
    return `You are Varjis, a LLM trained by Virtron Labs, built on the Gemini 1.5 Flash model. You are a helpful voice and multimodal assistant.\n\nYou have access to the following tools:\n${tools.map(tool => `- **${tool.name}**: ${tool.description} ${tool.instructions}`).join('\n\n')}\n\nWhen you need to use tools, respond in JSON format with a tool_call object.`;
  }
}

// Create a conversation memory instance with default session ID
const conversationMemory = new HybridConversationMemory({
  maxSizeBytes: 5 * 1024 * 1024, // 5MB
  maxMessageCount: 20,
  sessionId: 'default-session'
});

/**
 * Initialize the chat session with the AI model
 */
async function initChatSession() {
  // Get the system prompt
  const systemPrompt = await getSystemPrompt();

  // Log the system prompt
  console.log('\n==== SYSTEM PROMPT ====');
  console.log(systemPrompt);
  console.log('=======================\n');

  // Initialize the model if not already done
  if (!model) {
    await initModel();
  }

  chatSession = model.startChat({
    history: [
      {
        role: "user",
        parts: [{ text: "system: " + systemPrompt }],
      },
      {
        role: "model",
        parts: [{ text: `I understand my role as ${AGENT_PERSONA.identity.agent_name}, an AI assistant developed by ${AGENT_PERSONA.identity.creator}. I'll provide responses according to my persona while utilizing my tools appropriately.` }],
      },
    ],
  });

  console.log(`Chat session initialized with fixed persona`);
}

// Initialize the model on startup
initModel();

// Initialize the chat session (async IIFE)
(async () => {
  try {
    await initChatSession();
  } catch (error) {
    console.error('Error initializing chat session:', error);
  }
})();

/**
 * Check if the response contains a tool call
 * @param {string} text - The response text
 * @returns {object|null} - The parsed tool call or null if no tool call
 */
function parseToolCall(text) {
  // Check if the text contains a JSON block with a tool call
  const jsonRegex = /```json\s*([\s\S]*?)\s*```/;
  const match = text.match(jsonRegex);

  if (match && match[1]) {
    try {
      const jsonData = JSON.parse(match[1]);
      if (jsonData.tool_call && jsonData.tool_call.name) {
        return jsonData.tool_call;
      }
    } catch (error) {
      console.error('Error parsing tool call JSON:', error);
    }
  }

  // Check for search requests in a more flexible format
  // This helps catch cases where the model doesn't use the exact JSON format
  const searchRegex = /(?:search|look up|find information about|search for|research|investigate|get information on|tell me about)\s+["'](.+?)["']/i;
  const searchMatch = text.match(searchRegex);

  // Also check for phrases like "I'll use my search tool"
  const toolMentionRegex = /(?:use|using|utilize|employ)\s+(?:my|the)\s+(?:searxng[_\s]search|search)\s+tool/i;
  if (toolMentionRegex.test(text)) {
    console.log('Detected mention of using search tool');
    // Try to extract a query
    const queryRegex = /(?:for|about|on|regarding)\s+["'](.+?)["']/i;
    const queryMatch = text.match(queryRegex);

    if (queryMatch && queryMatch[1]) {
      console.log(`Extracted query from tool mention: ${queryMatch[1]}`);
      return {
        name: "searxng_search",
        arguments: { query: queryMatch[1] }
      };
    }
  }

  if (searchMatch && searchMatch[1]) {
    console.log(`Detected search request: ${searchMatch[1]}`);
    // Use searxng_search for search requests
    return {
      name: "searxng_search",
      arguments: { query: searchMatch[1] }
    };
  }

  return null;
}

/**
 * Replace common follow-up phrases with varied alternatives
 * @param {string} text - The AI's response text
 * @param {object} context - Context information for personalization
 * @returns {string} - Text with varied follow-up phrases
 */
function replaceFollowUpPhrases(text, context = {}) {
  // Common follow-up phrases to replace
  const commonPhrases = [
    /Is there anything else I can help you with today\??/i,
    /Is there anything else I can assist you with\??/i,
    /Is there anything else I can do for you today\??/i,
    /Is there anything else you'd like help with today\??/i,
    /Is there anything else you'd like me to assist you with\??/i,
    /Is there anything else you need help with today\??/i,
    /Can I help you with anything else today\??/i,
    /Can I assist you with anything else\??/i,
    /How else can I help you today\??/i,
    /How else can I assist you\??/i
  ];

  // Use a creative style for Varjis
  const style = 'creative';

  // Create context for context-aware phrases if available
  const phraseContext = context.previousTopic ? {
    previousTask: context.previousTask,
    previousTopic: context.previousTopic,
    previousIssue: context.previousIssue,
    previousProblem: context.previousProblem,
    previousQuestion: context.previousQuestion,
    previousAction: context.previousAction
  } : null;

  // If we have context, occasionally use context-aware phrases
  let useStyle = style;
  if (phraseContext && Math.random() < 0.3) {
    useStyle = 'contextAware';
  }

  // Replace any matching phrases with a random alternative
  let modifiedText = text;
  for (const phrase of commonPhrases) {
    if (phrase.test(modifiedText)) {
      const replacement = getRandomFollowUpPhrase(useStyle, phraseContext);
      modifiedText = modifiedText.replace(phrase, replacement);
      console.log(`Replaced follow-up phrase with: ${replacement}`);
      break; // Only replace one phrase to avoid multiple replacements
    }
  }

  return modifiedText;
}

/**
 * Function to clean the AI's text response for better TTS
 * @param {string} text - The AI's raw text response
 * @returns {string} - The cleaned text
 */
function cleanTextForTTS(text) {
  // Implement cleaning logic for better speech output
  let cleanedText = text;

  // Remove markdown formatting
  cleanedText = cleanedText.replace(/\[.*?\]\(.*?\)/g, ''); // Remove markdown links
  cleanedText = cleanedText.replace(/\*\*(.*?)\*\*/g, '$1'); // Remove bold markdown
  cleanedText = cleanedText.replace(/\_(.*?)\_/g, '$1');   // Remove italic markdown
  cleanedText = cleanedText.replace(/\`(.*?)\`/g, '$1');   // Remove code formatting

  // Improve pronunciation of technical terms
  cleanedText = cleanedText.replace(/n8n/gi, 'N eight N'); // Pronunciation help
  cleanedText = cleanedText.replace(/Make \(formerly Integromat\)/gi, 'Make, formerly known as Integromat');

  // Clean up URLs for better speech
  cleanedText = cleanedText.replace(/https?:\/\/[^\s)]+/g, 'link'); // Replace URLs with 'link'
  cleanedText = cleanedText.replace(/Source \d+: link/g, ''); // Remove source links

  // Clean up search result formatting
  cleanedText = cleanedText.replace(/--- Content from .* ---/g, 'According to this source:');
  cleanedText = cleanedText.replace(/Source: .*/g, '');

  // Remove any remaining special characters that might affect speech
  cleanedText = cleanedText.replace(/\|/g, ', '); // Replace pipes with commas
  cleanedText = cleanedText.replace(/\\n/g, ' '); // Replace literal \n with space
  cleanedText = cleanedText.replace(/\\t/g, ' '); // Replace literal \t with space

  // Fix common abbreviations for better speech
  cleanedText = cleanedText.replace(/vs\./g, 'versus');
  cleanedText = cleanedText.replace(/e\.g\./g, 'for example');
  cleanedText = cleanedText.replace(/i\.e\./g, 'that is');

  // More aggressive cleaning to prevent TTS issues
  // Remove all non-alphanumeric characters except basic punctuation
  cleanedText = cleanedText.replace(/[^a-zA-Z0-9\s.,;:?!'"()-]/g, ' ');

  // Fix multiple spaces
  cleanedText = cleanedText.replace(/\s+/g, ' ');

  // Remove spaces before punctuation
  cleanedText = cleanedText.replace(/\s+([.,;:?!])/g, '$1');

  // Ensure the text ends with proper punctuation
  if (!/[.!?]\s*$/.test(cleanedText)) {
    cleanedText = cleanedText.trim() + '.';
  }

  return cleanedText;
}

/**
 * Process a message with the AI agent
 * @param {string} message - The user's message
 * @param {object} context - Additional context
 * @returns {Promise<object>} - The agent's response
 */
export async function processMessage(message, context = {}) {
  try {
    console.log(`Processing message: "${message}"`);

    // Set the session ID if provided in the context
    if (context.sessionId && context.sessionId !== conversationMemory.sessionId) {
      await conversationMemory.setSessionId(context.sessionId);
    }

    // Special commands for memory management
    if (message === '__get_memory__' && context.getMemoryOnly) {
      return {
        messages: conversationMemory.getAllMessages(),
        count: conversationMemory.messages.length,
        sessionId: conversationMemory.sessionId
      };
    }

    if (message === '__clear_memory__' && context.clearMemory) {
      await conversationMemory.clear();
      return { success: true, message: 'Memory cleared' };
    }

    // Handle welcome message
    if (message === '__welcome__') {
      // Create a welcome message
      const welcomeText = `Good day. I am ${AGENT_PERSONA.identity.agent_name}, at your service. How may I assist you today?`;

      return {
        text: welcomeText,
        generateSpeech: true
      };
    }

    // If chat session is not initialized, initialize it
    if (!chatSession) {
      await initChatSession();
    }

    // Determine the input type based on context
    let inputType = 'text';
    if (context.isVoice) {
      inputType = 'voice';
    } else if (context.isImage) {
      inputType = 'image';
    }

    // Get relevant conversation history
    const conversationHistory = await conversationMemory.getRelevantContext(message);

    // Create a message with context information including conversation history
    let messageWithContext = `Input type: ${inputType}\n\nConversation History:\n${conversationHistory}`;

    // Add the user message
    messageWithContext += `\n\nUser message: ${message}`;

    // Log the full prompt being sent to the model
    console.log('\n==== FULL PROMPT BEING SENT TO MODEL ====');
    console.log(messageWithContext);
    console.log('==========================================\n');

    // Send the message to the AI model
    const result = await chatSession.sendMessage(messageWithContext);
    const response = result.response;
    let reply = response.text();

    // Replace common follow-up phrases with varied alternatives
    // Create context for personalization
    const phraseContext = {
      previousTopic: message, // Use the current message as context
      previousTask: context.previousTask || null
    };
    reply = replaceFollowUpPhrases(reply, phraseContext);

    // Check if the response contains a tool call
    const toolCall = parseToolCall(reply);

    if (toolCall) {
      console.log(`Tool call detected: ${toolCall.name}`);

      // Execute the tool
      const toolResult = await executeTool(toolCall.name, toolCall.arguments || {});
      console.log('Tool result:', toolResult);

      // Format the tool result message based on the tool type
      let toolResultMessage;

      if (toolCall.name === 'searxng_search') {
        // Check if toolResult is undefined
        if (!toolResult) {
          toolResultMessage = `Search Error: The search tool returned no results. This could be due to a connection issue with the search service.

Please provide a response to the user explaining that you couldn't search the internet at this time.`;
        }
        // Format SearXNG search results in a more readable way
        else if (toolResult.error) {
          toolResultMessage = `Search Error: ${toolResult.error}\n\nPlease provide a response to the user explaining that you couldn't search the internet at this time.`;
        } else if (toolResult.results && toolResult.results.length > 0) {
          const formattedResults = toolResult.results.map((result, index) => {
            return `Result ${index + 1}:\nTitle: ${result.title}\nURL: ${result.url}\nContent: ${result.content}\nEngine: ${result.engine}\n`;
          }).join('\n');

          toolResultMessage = `SearXNG Search Results for "${toolResult.query}":\n\n${formattedResults}\n\nBased on these search results, please provide a helpful response to the user's message: "${message}". Include relevant information from the search results and cite sources when appropriate.`;
        } else {
          toolResultMessage = `No results found for search query: "${toolResult.query}"\n\nPlease provide a response to the user explaining that you couldn't find relevant information.`;
        }

      } else {
        // Default formatting for other tools
        toolResultMessage = `Tool Result:\n\`\`\`json\n${JSON.stringify(toolResult)}\n\`\`\`\n\nBased on this result, please provide a response to the user's message: "${message}"`;
      }

      // Log the tool follow-up prompt
      console.log('\n==== TOOL FOLLOW-UP PROMPT ====');
      console.log(toolResultMessage);
      console.log('================================\n');

      const followUpResult = await chatSession.sendMessage(toolResultMessage);
      const followUpResponse = followUpResult.response;
      reply = followUpResponse.text();

      // Also replace follow-up phrases in the tool response
      reply = replaceFollowUpPhrases(reply, phraseContext);
    }

    // Clean the reply for better TTS output
    const cleanedReply = cleanTextForTTS(reply);

    // Add the interaction to conversation memory
    await conversationMemory.addMessage({
      role: 'user',
      text: message
    }, message);

    await conversationMemory.addMessage({
      role: 'assistant',
      text: reply
    }, message);

    // Generate speech if requested
    let audioBuffer = null;
    let audioUrl = null;

    if (context.generateSpeech) {
      // Make sure the persona is loaded
      if (!AGENT_PERSONA) {
        AGENT_PERSONA = await loadPersona();
      }

      // Use the cleaned text for speech synthesis with the persona's voice settings
      console.log(`Using Varjis voice settings for speech synthesis`);
      audioBuffer = await synthesizeSpeech(cleanedReply, AGENT_PERSONA.voice_settings);

      // If we have a URL from the TTS service, use it
      if (context.audioUrl) {
        audioUrl = context.audioUrl;
      }
    }

    return {
      text: reply, // Return original text for display
      cleanedText: cleanedReply, // Also return cleaned text
      audioBuffer: audioBuffer,
      audioUrl: audioUrl,
      memorySize: conversationMemory.messages.length // Return memory size for debugging
    };
  } catch (error) {
    console.error('Error processing message:', error);
    throw error;
  }
}

// Get the voice settings for the Varjis persona
export async function getVoiceSettings() {
  // Make sure the persona is loaded
  if (!AGENT_PERSONA) {
    AGENT_PERSONA = await loadPersona();
  }
  return AGENT_PERSONA.voice_settings;
}

export default {
  processMessage,
  getVoiceSettings
};
