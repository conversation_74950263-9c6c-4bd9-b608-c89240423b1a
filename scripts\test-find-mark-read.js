/**
 * Test script for finding and marking emails as read
 * 
 * Run with: node scripts/test-find-mark-read.js "subject" "sender"
 * 
 * Examples:
 * - node scripts/test-find-mark-read.js "YouTube" ""
 * - node scripts/test-find-mark-read.js "" "<EMAIL>"
 * - node scripts/test-find-mark-read.js "Invoice" "billing"
 */

import { findAndMarkAsRead } from '../email.js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

async function testFindAndMarkAsRead() {
  try {
    // Get the command line arguments
    const subject = process.argv[2] || '';
    const sender = process.argv[3] || '';
    
    console.log(`Testing find and mark as read:`);
    console.log(`Subject: "${subject}"`);
    console.log(`Sender: "${sender}"`);
    
    const result = await findAndMarkAsRead(subject, sender);
    
    console.log('\nResult:');
    console.log(JSON.stringify(result, null, 2));
    
  } catch (error) {
    console.error('Error:', error);
  }
}

// Run the test
testFindAndMarkAsRead();