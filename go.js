import fs from "fs";
import { google } from "googleapis";
import path from 'path';
import { fileURLToPath } from 'url';
import { simpleParser } from 'mailparser';
import { GoogleGenerativeAI } from "@google/generative-ai";
import dotenv from 'dotenv';

dotenv.config();

// Get the directory name
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Path to token file
const TOKEN_PATH = path.join(__dirname, 'token.json'); // Simplified path

// Initialize the OAuth client using token.json file
function getOAuthClient() {
    // Check if token file exists
    if (!fs.existsSync(TOKEN_PATH)) {
        throw new Error("Token file not found. Please authenticate first.");
    }

    // Read token data
    const tokenData = JSON.parse(fs.readFileSync(TOKEN_PATH));

    // Create OAuth client with the actual credentials
    const oAuth2Client = new google.auth.OAuth2(
        '122591791863-e7o6l17e9k3i4e6kogr96t99hdffvkma.apps.googleusercontent.com',   // Client ID
        '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',                                           // Client Secret
        'http://localhost:3081/oauth2callback'                                           // Redirect URI
    );

    // Set the credentials from the token file
    oAuth2Client.setCredentials(tokenData);

    return oAuth2Client;
}

/**
 * Fetch and parse unread emails from Gmail
 * @param {number} maxResults - Maximum number of emails to fetch
 * @returns {Array} - Array of parsed email objects
 */
async function getUnreadEmails(maxResults = 5) {
    try {
        const auth = getOAuthClient();
        const gmail = google.gmail({ version: "v1", auth });

        const res = await gmail.users.messages.list({
            userId: "me",
            labelIds: ["INBOX"],
            q: "is:unread",
            maxResults: maxResults,
        });

        if (!res.data.messages) {
            return []; // Return empty array if no unread emails
        }

        const emails = [];
        for (const msg of res.data.messages) {
            const email = await gmail.users.messages.get({
                userId: "me",
                id: msg.id,
                format: "raw" // Get raw message to parse with mailparser
            });

            // Decode the raw message
            const rawEmail = Buffer.from(email.data.raw, 'base64').toString('utf8');

            // Parse the email using mailparser
            const parsedEmail = await simpleParser(rawEmail);

            emails.push({
                id: msg.id,
                from: parsedEmail.from?.text || "N/A",
                to: parsedEmail.to?.text || "N/A",
                subject: parsedEmail.subject || "N/A",
                date: parsedEmail.date || new Date(),
                text: parsedEmail.text || "",
                html: parsedEmail.html || "",
                textAsHtml: parsedEmail.textAsHtml || "",
                body: parsedEmail.html || parsedEmail.text || "",
                attachments: parsedEmail.attachments || [],
                headerLines: parsedEmail.headerLines || [],
                headers: parsedEmail.headers || {},
                messageId: parsedEmail.messageId || "",
                unread: email.data.labelIds.includes("UNREAD")
            });
        }

        return emails;
    } catch (error) {
        console.error("Error fetching emails:", error);
        return []; // Return empty array on error
    }
}

// Initialize Gemini
const genAI = new GoogleGenerativeAI(process.env.GOOGLE_API_KEY);
const preparationModel = genAI.getGenerativeModel({ model: "gemini-1.5-flash" }); // Choose appropriate model

// Prompt Template for the Preparation Agent
const preparationPrompt = `You are an AI agent designed to prepare email data for an importance scoring LLM.
Your task is to take the raw email data, the output of a Natural Language Understanding (NLU) analysis, user context, and importance scoring instructions and structure them into a concise JSON object that will be given to the scoring LLM.

Here's the raw email data:
\`\`\`json
{{email_data}}\`\`\`

Here's the NLU analysis of the email:
\`\`\`json
{{nlu_analysis}}\`\`\`

Here's the user's context:
\`\`\`json
{{user_context}}\`\`\`

Here are the importance scoring instructions that the scoring LLM will use:
\`\`\`json
{{importance_scoring_instructions}}\`\`\`

Based on all of the information above, create a JSON object with the following structure:

\`\`\`json
{
  "email_summary": "(A concise summary of the email's content)",
  "sender_importance": "(Sender's importance level, if available)",
  "intent": "(The primary intent of the email)",
  "sentiment": "(The sentiment of the email)",
  "urgency_indicators": "(List of urgency indicators found in the email)",
  "has_attachment": "(Boolean: true if the email has an attachment, false otherwise)",
  "user_preferences": "(Relevant user preferences that might affect importance)",
  "scoring_instructions": "(Copy of the importance scoring instructions)",
  "reasoning_prompt": "(A short prompt to guide the scoring LLM's reasoning)"
}
\`\`\`

Ensure the output is a valid JSON object.`;

/**
 * Prepares email data for scoring by an LLM using @google/generative-ai.
 * @param {object} email - The raw email data object.
 * @param {object} nluAnalysis - The NLU analysis result from the first LLM.
 * @param {object} userContext - The user's context information.
 * @param {object} importanceScoringInstructions - The instructions for scoring email importance.
 * @returns {object|null} - A JSON object ready for the scoring LLM, or null on error.
 */
async function prepareEmailForScoring(email, nluAnalysis, userContext, importanceScoringInstructions) {
    try {
        const emailData = {
            from: email.from,
            to: email.to,
            subject: email.subject,
            date: email.date,
            text: email.text,
            html: email.html,
            textAsHtml: email.textAsHtml,
            body: email.body,
            attachments: email.attachments,
            headerLines: email.headerLines,
            headers: email.headers,
            messageId: email.messageId
        };

        const prompt = preparationPrompt
            .replace("{{email_data}}", JSON.stringify(emailData, null, 2))
            .replace("{{nlu_analysis}}", JSON.stringify(nluAnalysis, null, 2))
            .replace("{{user_context}}", JSON.stringify(userContext, null, 2))
            .replace("{{importance_scoring_instructions}}", JSON.stringify(importanceScoringInstructions, null, 2));

        const result = await preparationModel.generateContent([prompt]);
        const responseText = result.response?.candidates?.[0]?.content?.parts?.[0]?.text;

        if (responseText) {
            try {
                // Attempt to parse the response as JSON
                let jsonString = responseText.trim();
                if (jsonString.startsWith("```")) {
                    jsonString = jsonString.replace(/^```(?:json)?/, '').replace(/```$/, '').trim();
                }
                const preparedData = JSON.parse(jsonString);
                return preparedData;
            } catch (parseError) {
                console.error("Error parsing LLM response:", parseError, responseText);
                return null;
            }
        } else {
            console.warn("Preparation LLM returned empty response.");
            return null;
        }

    } catch (error) {
        console.error("Error preparing email for scoring:", error);
        return null;
    }
}

// Update the main function to show and save plain text email content and prepare for scoring
async function main() {
    try {
        const emails = await getUnreadEmails(5); // Get 5 emails
        if (emails.length > 0) {
            console.log("Unread Emails:");
            for (const email of emails) {
                console.log(`\n- From: ${email.from}`);
                console.log(`  Subject: ${email.subject}`);
                console.log(`  Date: ${email.date}`);

                // Get the plain text content
                const textContent = email.text || "No text content available";
                console.log(`  Text Preview: ${textContent.substring(0, 150)}...`);

                // Save the plain text content to a file
                fs.writeFileSync(`email_${email.id}.txt`, textContent);
                console.log(`  Saved plain text to: email_${email.id}.txt`);

                // Sample NLU Analysis (Replace with your actual NLU LLM output)
                const sampleNluAnalysis = {
                    sentiment: { label: "neutral", confidence: 0.7 },
                    intent: { label: "unknown", confidence: 0.5 },
                    entities: {},
                    urgency_indicators: [],
                    keywords: []
                };

                // Sample User Context (Replace with your actual user context)
                const sampleUserContext = {
                    current_time: new Date().toISOString(),
                    current_location: "Winter Haven, Florida, United States",
                    user_preferences: {
                        prioritize_from_vip: true,
                        keywords_high_priority: ["important"],
                        projects_of_interest: []
                    }
                };

                // Sample Importance Scoring Instructions (Replace with your actual instructions)
                const sampleImportanceScoringInstructions = {
                    high_priority_indicators: [],
                    medium_priority_indicators: [],
                    weighting_factors: {},
                    scoring_logic_description: "Score based on basic email metadata."
                };

                // Prepare the email data for scoring
                const preparedDataForScoring = await prepareEmailForScoring(
                    email,
                    sampleNluAnalysis,
                    sampleUserContext,
                    sampleImportanceScoringInstructions
                );

                if (preparedDataForScoring) {
                    console.log("\n--- Prepared Data for Scoring LLM ---");
                    console.log(JSON.stringify(preparedDataForScoring, null, 2));
                    // Here you would send 'preparedDataForScoring' to your scoring LLM
                } else {
                    console.warn(`Failed to prepare data for scoring email ${email.id}`);
                }
            }
        } else {
            console.log("No unread emails found.");
        }
    } catch (error) {
        console.error("Error in main:", error);
    }
}

main();